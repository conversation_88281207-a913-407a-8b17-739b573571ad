<?php

namespace LYstore\Core;

/**
 * فئة إدارة استجابات HTTP
 */
class Response
{
    private $headers = [];
    private $statusCode = 200;

    /**
     * تعيين رأس HTTP
     */
    public function setHeader(string $name, string $value): self
    {
        $this->headers[$name] = $value;
        return $this;
    }

    /**
     * تعيين رمز الحالة
     */
    public function setStatusCode(int $code): self
    {
        $this->statusCode = $code;
        return $this;
    }

    /**
     * إرسال استجابة JSON
     */
    public function json(array $data, int $statusCode = 200): void
    {
        $this->setStatusCode($statusCode);
        $this->setHeader('Content-Type', 'application/json; charset=utf-8');
        
        $this->sendHeaders();
        
        echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit;
    }

    /**
     * إعادة توجيه
     */
    public function redirect(string $url, int $statusCode = 302): void
    {
        $this->setStatusCode($statusCode);
        $this->setHeader('Location', $url);
        
        $this->sendHeaders();
        exit;
    }

    /**
     * إرسال محتوى نصي
     */
    public function text(string $content, int $statusCode = 200): void
    {
        $this->setStatusCode($statusCode);
        $this->setHeader('Content-Type', 'text/plain; charset=utf-8');
        
        $this->sendHeaders();
        
        echo $content;
        exit;
    }

    /**
     * إرسال محتوى HTML
     */
    public function html(string $content, int $statusCode = 200): void
    {
        $this->setStatusCode($statusCode);
        $this->setHeader('Content-Type', 'text/html; charset=utf-8');
        
        $this->sendHeaders();
        
        echo $content;
        exit;
    }

    /**
     * تحميل ملف
     */
    public function download(string $filePath, string $fileName = null): void
    {
        if (!file_exists($filePath)) {
            $this->setStatusCode(404);
            $this->sendHeaders();
            echo 'الملف غير موجود';
            exit;
        }

        $fileName = $fileName ?: basename($filePath);
        $fileSize = filesize($filePath);
        $mimeType = mime_content_type($filePath) ?: 'application/octet-stream';

        $this->setHeader('Content-Type', $mimeType);
        $this->setHeader('Content-Disposition', 'attachment; filename="' . $fileName . '"');
        $this->setHeader('Content-Length', (string) $fileSize);
        $this->setHeader('Cache-Control', 'must-revalidate');
        $this->setHeader('Pragma', 'public');

        $this->sendHeaders();

        readfile($filePath);
        exit;
    }

    /**
     * عرض ملف في المتصفح
     */
    public function file(string $filePath): void
    {
        if (!file_exists($filePath)) {
            $this->setStatusCode(404);
            $this->sendHeaders();
            echo 'الملف غير موجود';
            exit;
        }

        $mimeType = mime_content_type($filePath) ?: 'application/octet-stream';
        $fileSize = filesize($filePath);

        $this->setHeader('Content-Type', $mimeType);
        $this->setHeader('Content-Length', (string) $fileSize);

        $this->sendHeaders();

        readfile($filePath);
        exit;
    }

    /**
     * إرسال رؤوس HTTP
     */
    private function sendHeaders(): void
    {
        if (headers_sent()) {
            return;
        }

        // إرسال رمز الحالة
        http_response_code($this->statusCode);

        // إرسال الرؤوس المخصصة
        foreach ($this->headers as $name => $value) {
            header("{$name}: {$value}");
        }
    }

    /**
     * إرسال استجابة خطأ
     */
    public function error(string $message, int $statusCode = 500): void
    {
        $this->json(['error' => $message], $statusCode);
    }

    /**
     * إرسال استجابة نجاح
     */
    public function success(string $message, array $data = []): void
    {
        $response = ['success' => true, 'message' => $message];
        
        if (!empty($data)) {
            $response['data'] = $data;
        }
        
        $this->json($response);
    }

    /**
     * إرسال استجابة تحقق من صحة البيانات
     */
    public function validationError(array $errors): void
    {
        $this->json([
            'success' => false,
            'message' => 'بيانات غير صحيحة',
            'errors' => $errors
        ], 422);
    }

    /**
     * تعيين كوكي
     */
    public function setCookie(string $name, string $value, int $expire = 0, string $path = '/', string $domain = '', bool $secure = false, bool $httpOnly = true): void
    {
        setcookie($name, $value, $expire, $path, $domain, $secure, $httpOnly);
    }

    /**
     * حذف كوكي
     */
    public function deleteCookie(string $name, string $path = '/', string $domain = ''): void
    {
        $this->setCookie($name, '', time() - 3600, $path, $domain);
    }
}

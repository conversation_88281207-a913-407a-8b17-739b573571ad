<?php
// بدء التخزين المؤقت للمحتوى
ob_start();
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt me-2"></i>
        لوحة التحكم
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshDashboard()">
                <i class="fas fa-sync-alt me-1"></i>
                تحديث
            </button>
        </div>
    </div>
</div>

<!-- ترحيب -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-info border-0" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <h4 class="alert-heading">
                <i class="fas fa-sun me-2"></i>
                مرحباً، <?= htmlspecialchars($user['name']) ?>!
            </h4>
            <p class="mb-0">
                أهلاً بك في نظام LYstoreE لإدارة المبيعات والمشتريات والمخازن.
                <br>
                <small>دورك في النظام: <strong><?= htmlspecialchars($user['role']) ?></strong></small>
            </p>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <!-- إحصائيات العملاء -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            العملاء
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format($stats['customers']['total'] ?? 0) ?>
                        </div>
                        <div class="text-xs text-muted">
                            نشط: <?= number_format($stats['customers']['active'] ?? 0) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات المنتجات -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            المنتجات
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format($stats['products']['total'] ?? 0) ?>
                        </div>
                        <div class="text-xs text-muted">
                            مخزون منخفض: <?= number_format($stats['products']['low_stock'] ?? 0) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-box fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات المبيعات -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            مبيعات اليوم
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format($stats['sales']['today'] ?? 0, 2) ?> د.ل
                        </div>
                        <div class="text-xs text-muted">
                            هذا الشهر: <?= number_format($stats['sales']['this_month'] ?? 0, 2) ?> د.ل
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات المشتريات -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            مشتريات اليوم
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format($stats['purchases']['today'] ?? 0, 2) ?> د.ل
                        </div>
                        <div class="text-xs text-muted">
                            أوامر معلقة: <?= number_format($stats['purchases']['pending_orders'] ?? 0) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- التنبيهات -->
<?php if (!empty($alerts)): ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bell me-2"></i>
                    التنبيهات
                </h5>
            </div>
            <div class="card-body">
                <?php foreach ($alerts as $alert): ?>
                    <div class="alert alert-<?= $alert['type'] ?> alert-dismissible fade show" role="alert">
                        <strong><?= htmlspecialchars($alert['title']) ?></strong>
                        <?= htmlspecialchars($alert['message']) ?>
                        <?php if (isset($alert['link'])): ?>
                            <a href="<?= htmlspecialchars($alert['link']) ?>" class="alert-link">عرض التفاصيل</a>
                        <?php endif; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- البيانات الحديثة -->
<div class="row">
    <!-- أحدث العملاء -->
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>
                    أحدث العملاء
                </h5>
                <a href="/customers" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_data['recent_customers'])): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>النوع</th>
                                    <th>تاريخ الإضافة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_data['recent_customers'] as $customer): ?>
                                    <tr>
                                        <td>
                                            <a href="/customers/<?= $customer['customer_id'] ?>" class="text-decoration-none">
                                                <?= htmlspecialchars($customer['name']) ?>
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?= htmlspecialchars($customer['type']) ?></span>
                                        </td>
                                        <td class="text-muted">
                                            <?= date('Y-m-d', strtotime($customer['created_at'])) ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-muted text-center">لا توجد بيانات</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- أحدث أوامر البيع -->
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>
                    أحدث أوامر البيع
                </h5>
                <a href="/sales-orders" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_data['recent_sales_orders'])): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>رقم الأمر</th>
                                    <th>العميل</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_data['recent_sales_orders'] as $order): ?>
                                    <tr>
                                        <td>
                                            <a href="/sales-orders/<?= $order['order_id'] ?>" class="text-decoration-none">
                                                <?= htmlspecialchars($order['order_number']) ?>
                                            </a>
                                        </td>
                                        <td><?= htmlspecialchars($order['customer_name']) ?></td>
                                        <td><?= number_format($order['total_amount'], 2) ?> د.ل</td>
                                        <td>
                                            <span class="badge bg-info"><?= htmlspecialchars($order['status']) ?></span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-muted text-center">لا توجد بيانات</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- المنتجات منخفضة المخزون -->
<?php if (!empty($recent_data['low_stock_products'])): ?>
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2 text-warning"></i>
                    منتجات بمخزون منخفض
                </h5>
                <a href="/products?filter=low_stock" class="btn btn-sm btn-outline-warning">عرض الكل</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>اسم المنتج</th>
                                <th>المخزون الحالي</th>
                                <th>الحد الأدنى</th>
                                <th>سعر البيع</th>
                                <th>الإجراء</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_data['low_stock_products'] as $product): ?>
                                <tr>
                                    <td>
                                        <a href="/products/<?= $product['product_id'] ?>" class="text-decoration-none">
                                            <?= htmlspecialchars($product['name']) ?>
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger"><?= number_format($product['current_stock'], 2) ?></span>
                                    </td>
                                    <td><?= number_format($product['min_stock_level'], 2) ?></td>
                                    <td><?= number_format($product['unit_price'], 2) ?> د.ل</td>
                                    <td>
                                        <a href="/purchase-orders/create?product_id=<?= $product['product_id'] ?>" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-plus me-1"></i>
                                            طلب شراء
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
function refreshDashboard() {
    showLoading();
    location.reload();
}

// تحديث تلقائي كل 5 دقائق
setInterval(function() {
    // يمكن إضافة تحديث AJAX هنا لاحقاً
}, 300000);
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS lystore
  DEFAULT CHARACTER SET utf8mb4
  COLLATE utf8mb4_general_ci;
USE lystore;

-- جدول الأدوار
CREATE TABLE roles (
    role_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد للدور',
    role_name VARCHAR(50) NOT NULL COMMENT 'اسم الدور أو الصلاحية',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB COMMENT='جدول الأدوار والصلاحيات';

-- جدول المستخدمين
CREATE TABLE users (
    user_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد للمستخدم',
    name VARCHAR(100) NOT NULL COMMENT 'الاسم الكامل',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT 'اسم المستخدم لتسجيل الدخول',
    password_hash VARCHAR(255) NOT NULL COMMENT 'كلمة المرور المشفرة',
    email VARCHAR(100) UNIQUE COMMENT 'البريد الإلكتروني',
    phone VARCHAR(20) COMMENT 'رقم الهاتف',
    role_id INT COMMENT 'معرف الدور المرتبط',
    status ENUM('active','inactive') DEFAULT 'active' COMMENT 'حالة المستخدم',
    last_login TIMESTAMP NULL COMMENT 'آخر تسجيل دخول',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(role_id)
) ENGINE=InnoDB COMMENT='جدول المستخدمين';

-- جدول العملاء
CREATE TABLE customers (
    customer_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد للعميل',
    name VARCHAR(100) NOT NULL COMMENT 'اسم العميل',
    phone VARCHAR(20) COMMENT 'رقم الهاتف',
    email VARCHAR(100) COMMENT 'البريد الإلكتروني',
    address TEXT COMMENT 'العنوان',
    city VARCHAR(50) COMMENT 'المدينة',
    type ENUM('جملة','تجزئة','شركة') DEFAULT 'شركة' COMMENT 'نوع العميل',
    credit_limit DECIMAL(12,2) DEFAULT 0.00 COMMENT 'حد الائتمان',
    payment_terms VARCHAR(100) COMMENT 'شروط الدفع',
    tax_number VARCHAR(50) COMMENT 'الرقم الضريبي',
    status ENUM('active','inactive') DEFAULT 'active' COMMENT 'حالة العميل',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB COMMENT='جدول العملاء';

-- جدول الموردين
CREATE TABLE suppliers (
    supplier_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد للمورد',
    name VARCHAR(100) NOT NULL COMMENT 'اسم المورد',
    phone VARCHAR(20) COMMENT 'رقم الهاتف',
    email VARCHAR(100) COMMENT 'البريد الإلكتروني',
    address TEXT COMMENT 'العنوان',
    city VARCHAR(50) COMMENT 'المدينة',
    country VARCHAR(50) DEFAULT 'ليبيا' COMMENT 'البلد',
    payment_terms VARCHAR(100) COMMENT 'شروط الدفع',
    tax_number VARCHAR(50) COMMENT 'الرقم الضريبي',
    bank_details TEXT COMMENT 'تفاصيل البنك',
    status ENUM('active','inactive') DEFAULT 'active' COMMENT 'حالة المورد',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB COMMENT='جدول الموردين';

-- جدول التصنيفات
CREATE TABLE categories (
    category_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد للتصنيف',
    category_name VARCHAR(100) NOT NULL COMMENT 'اسم التصنيف',
    parent_id INT NULL COMMENT 'التصنيف الأب للتصنيفات الفرعية',
    description TEXT COMMENT 'وصف التصنيف',
    status ENUM('active','inactive') DEFAULT 'active' COMMENT 'حالة التصنيف',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(category_id)
) ENGINE=InnoDB COMMENT='جدول تصنيفات المنتجات';

-- جدول الوحدات
CREATE TABLE units (
    unit_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد للوحدة',
    unit_name VARCHAR(50) NOT NULL COMMENT 'اسم الوحدة (كيلو، قطعة، متر)',
    unit_symbol VARCHAR(10) COMMENT 'رمز الوحدة (كجم، قطعة، م)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB COMMENT='جدول وحدات القياس';

-- جدول المنتجات
CREATE TABLE products (
    product_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد للمنتج',
    name VARCHAR(150) NOT NULL COMMENT 'اسم المنتج',
    description TEXT COMMENT 'وصف المنتج',
    category_id INT COMMENT 'معرف التصنيف',
    unit_id INT COMMENT 'معرف وحدة القياس',
    barcode VARCHAR(50) UNIQUE COMMENT 'الباركود',
    sku VARCHAR(50) UNIQUE COMMENT 'رمز المنتج',
    unit_price DECIMAL(10,2) NOT NULL COMMENT 'سعر البيع',
    wholesale_price DECIMAL(10,2) COMMENT 'سعر الجملة',
    cost_price DECIMAL(10,2) NOT NULL COMMENT 'سعر التكلفة',
    tax_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT 'نسبة الضريبة',
    min_stock_level DECIMAL(10,2) DEFAULT 0.00 COMMENT 'الحد الأدنى للمخزون',
    max_stock_level DECIMAL(10,2) DEFAULT 0.00 COMMENT 'الحد الأقصى للمخزون',
    current_stock DECIMAL(10,2) DEFAULT 0.00 COMMENT 'المخزون الحالي',
    expiry_date DATE COMMENT 'تاريخ الصلاحية',
    image_path VARCHAR(255) COMMENT 'مسار صورة المنتج',
    status ENUM('active','inactive','discontinued') DEFAULT 'active' COMMENT 'حالة المنتج',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(category_id),
    FOREIGN KEY (unit_id) REFERENCES units(unit_id)
) ENGINE=InnoDB COMMENT='جدول المنتجات';

-- جدول المستودعات
CREATE TABLE warehouses (
    warehouse_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد للمستودع',
    name VARCHAR(100) NOT NULL COMMENT 'اسم المستودع',
    location VARCHAR(255) COMMENT 'موقع المستودع',
    manager_id INT COMMENT 'معرف مدير المستودع',
    status ENUM('active','inactive') DEFAULT 'active' COMMENT 'حالة المستودع',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (manager_id) REFERENCES users(user_id)
) ENGINE=InnoDB COMMENT='جدول المستودعات';

-- جدول مخزون المنتجات في المستودعات
CREATE TABLE product_warehouse_stock (
    stock_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد للمخزون',
    product_id INT COMMENT 'معرف المنتج',
    warehouse_id INT COMMENT 'معرف المستودع',
    quantity DECIMAL(10,2) DEFAULT 0.00 COMMENT 'الكمية المتوفرة',
    reserved_qty DECIMAL(10,2) DEFAULT 0.00 COMMENT 'الكمية المحجوزة',
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(product_id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(warehouse_id),
    UNIQUE KEY unique_product_warehouse (product_id, warehouse_id)
) ENGINE=InnoDB COMMENT='جدول مخزون المنتجات في المستودعات';

-- جدول أوامر البيع
CREATE TABLE sales_orders (
    order_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد لأمر البيع',
    order_number VARCHAR(50) NOT NULL UNIQUE COMMENT 'رقم أمر البيع',
    customer_id INT COMMENT 'معرف العميل',
    user_id INT COMMENT 'معرف المستخدم الذي أنشأ الأمر',
    order_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الطلب',
    delivery_date DATE COMMENT 'تاريخ التسليم المطلوب',
    status ENUM('جديد','قيد التنفيذ','مكتمل','ملغي','مؤجل') DEFAULT 'جديد' COMMENT 'حالة الطلب',
    subtotal DECIMAL(12,2) DEFAULT 0.00 COMMENT 'المجموع الفرعي',
    tax_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT 'قيمة الضريبة',
    discount_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT 'قيمة الخصم',
    total_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT 'المجموع الإجمالي',
    notes TEXT COMMENT 'ملاحظات',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(customer_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
) ENGINE=InnoDB COMMENT='جدول أوامر البيع';

-- تفاصيل أوامر البيع
CREATE TABLE sales_order_items (
    order_item_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد لبند الطلب',
    order_id INT COMMENT 'معرف أمر البيع',
    product_id INT COMMENT 'معرف المنتج',
    quantity DECIMAL(10,2) NOT NULL COMMENT 'الكمية المطلوبة',
    unit_price DECIMAL(10,2) NOT NULL COMMENT 'سعر الوحدة',
    discount_percent DECIMAL(5,2) DEFAULT 0.00 COMMENT 'نسبة الخصم',
    tax_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT 'نسبة الضريبة',
    line_total DECIMAL(12,2) NOT NULL COMMENT 'إجمالي السطر',
    notes TEXT COMMENT 'ملاحظات البند',
    FOREIGN KEY (order_id) REFERENCES sales_orders(order_id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(product_id)
) ENGINE=InnoDB COMMENT='جدول تفاصيل أوامر البيع';

-- أوامر الشراء
CREATE TABLE purchase_orders (
    po_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد لأمر الشراء',
    po_number VARCHAR(50) NOT NULL UNIQUE COMMENT 'رقم أمر الشراء',
    supplier_id INT COMMENT 'معرف المورد',
    user_id INT COMMENT 'معرف المستخدم الذي أنشأ الأمر',
    order_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ أمر الشراء',
    expected_delivery_date DATE COMMENT 'تاريخ التسليم المتوقع',
    status ENUM('جديد','مرسل','قيد التنفيذ','مكتمل','ملغي') DEFAULT 'جديد' COMMENT 'حالة الأمر',
    subtotal DECIMAL(12,2) DEFAULT 0.00 COMMENT 'المجموع الفرعي',
    tax_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT 'قيمة الضريبة',
    total_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT 'المجموع الإجمالي',
    notes TEXT COMMENT 'ملاحظات',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(supplier_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
) ENGINE=InnoDB COMMENT='جدول أوامر الشراء';

-- تفاصيل أوامر الشراء
CREATE TABLE purchase_order_items (
    po_item_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد لبند الشراء',
    po_id INT COMMENT 'معرف أمر الشراء',
    product_id INT COMMENT 'معرف المنتج',
    quantity DECIMAL(10,2) NOT NULL COMMENT 'الكمية',
    unit_cost DECIMAL(10,2) NOT NULL COMMENT 'سعر التكلفة للوحدة',
    tax_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT 'نسبة الضريبة',
    line_total DECIMAL(12,2) NOT NULL COMMENT 'إجمالي السطر',
    received_qty DECIMAL(10,2) DEFAULT 0.00 COMMENT 'الكمية المستلمة',
    notes TEXT COMMENT 'ملاحظات البند',
    FOREIGN KEY (po_id) REFERENCES purchase_orders(po_id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(product_id)
) ENGINE=InnoDB COMMENT='جدول تفاصيل أوامر الشراء';

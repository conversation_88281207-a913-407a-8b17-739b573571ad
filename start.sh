#!/bin/bash

# نظام LYstoreE - ملف بدء التشغيل لـ Linux/Mac

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دالة لطباعة رسالة ملونة
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# دالة للتحقق من وجود أمر
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# دالة للتحقق من توفر منفذ
is_port_available() {
    local port=$1
    ! lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1
}

# دالة للبحث عن منفذ متاح
find_available_port() {
    local start_port=$1
    local port=$start_port
    
    while ! is_port_available $port; do
        ((port++))
        if [ $port -gt $((start_port + 100)) ]; then
            echo $start_port
            return
        fi
    done
    
    echo $port
}

# عرض الترحيب
clear
print_message $BLUE "========================================"
print_message $BLUE "    نظام LYstoreE للمبيعات والمشتريات"
print_message $BLUE "========================================"
echo

# التحقق من وجود PHP
if ! command_exists php; then
    print_message $RED "خطأ: PHP غير مثبت"
    print_message $YELLOW "يرجى تثبيت PHP 8.0 أو أحدث"
    exit 1
fi

# التحقق من إصدار PHP
PHP_VERSION=$(php -r "echo PHP_VERSION;")
PHP_MAJOR=$(echo $PHP_VERSION | cut -d. -f1)
PHP_MINOR=$(echo $PHP_VERSION | cut -d. -f2)

if [ $PHP_MAJOR -lt 8 ]; then
    print_message $RED "خطأ: يتطلب النظام PHP 8.0 أو أحدث"
    print_message $YELLOW "الإصدار الحالي: $PHP_VERSION"
    exit 1
fi

print_message $GREEN "تم العثور على PHP $PHP_VERSION"

# التحقق من الامتدادات المطلوبة
REQUIRED_EXTENSIONS=("pdo" "pdo_mysql" "mbstring" "json" "openssl")
MISSING_EXTENSIONS=()

for ext in "${REQUIRED_EXTENSIONS[@]}"; do
    if ! php -m | grep -q "^$ext$"; then
        MISSING_EXTENSIONS+=($ext)
    fi
done

if [ ${#MISSING_EXTENSIONS[@]} -ne 0 ]; then
    print_message $RED "امتدادات PHP مطلوبة غير مثبتة:"
    printf '%s\n' "${MISSING_EXTENSIONS[@]}"
    exit 1
fi

# التحقق من وجود ملفات النظام
if [ ! -f "public/index.php" ]; then
    print_message $RED "خطأ: ملفات النظام غير موجودة"
    print_message $YELLOW "تأكد من وجودك في مجلد LYstoreE الصحيح"
    exit 1
fi

# إنشاء المجلدات المطلوبة
REQUIRED_DIRS=("storage/logs" "storage/sessions" "storage/backups" "storage/cache" "public/uploads")

for dir in "${REQUIRED_DIRS[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        print_message $GREEN "تم إنشاء المجلد: $dir"
    fi
done

# تعيين الصلاحيات
chmod -R 755 storage/ 2>/dev/null
chmod -R 755 public/uploads/ 2>/dev/null

print_message $GREEN "تم التحقق من المتطلبات بنجاح!"
echo

# القائمة الرئيسية
show_menu() {
    echo "اختر أحد الخيارات التالية:"
    echo
    echo "1. تشغيل خادم التطوير"
    echo "2. فتح معالج التثبيت"
    echo "3. عرض معلومات النظام"
    echo "4. إنشاء ملف .env"
    echo "5. تعيين الصلاحيات"
    echo "6. الخروج"
    echo
}

# تشغيل خادم التطوير
start_server() {
    echo
    print_message $BLUE "بدء تشغيل خادم التطوير..."
    echo
    
    local port=$(find_available_port 8000)
    local host="127.0.0.1"
    
    print_message $GREEN "سيتم تشغيل الخادم على: http://$host:$port"
    print_message $YELLOW "اضغط Ctrl+C لإيقاف الخادم"
    echo
    
    # فتح المتصفح تلقائياً (إذا كان متاحاً)
    if command_exists xdg-open; then
        xdg-open "http://$host:$port" >/dev/null 2>&1 &
    elif command_exists open; then
        open "http://$host:$port" >/dev/null 2>&1 &
    fi
    
    # تشغيل الخادم
    php -S $host:$port -t public
}

# فتح معالج التثبيت
open_installer() {
    echo
    print_message $BLUE "فتح معالج التثبيت..."
    echo
    
    if [ ! -f "install/install.php" ]; then
        print_message $RED "ملف التثبيت غير موجود!"
        return
    fi
    
    local port=$(find_available_port 8001)
    local host="127.0.0.1"
    local url="http://$host:$port/install/install.php"
    
    print_message $GREEN "سيتم فتح معالج التثبيت على: $url"
    
    # فتح المتصفح
    if command_exists xdg-open; then
        xdg-open "$url" >/dev/null 2>&1 &
    elif command_exists open; then
        open "$url" >/dev/null 2>&1 &
    fi
    
    # تشغيل خادم مؤقت
    php -S $host:$port -t .
}

# عرض معلومات النظام
show_system_info() {
    echo
    print_message $BLUE "========== معلومات النظام =========="
    echo
    
    # معلومات PHP
    print_message $GREEN "إصدار PHP:"
    php --version | head -1
    echo
    
    print_message $GREEN "الامتدادات المثبتة:"
    for ext in "${REQUIRED_EXTENSIONS[@]}"; do
        if php -m | grep -q "^$ext$"; then
            print_message $GREEN "✓ $ext"
        else
            print_message $RED "✗ $ext"
        fi
    done
    echo
    
    print_message $GREEN "إعدادات PHP المهمة:"
    php -r "echo 'memory_limit: ' . ini_get('memory_limit') . PHP_EOL;"
    php -r "echo 'upload_max_filesize: ' . ini_get('upload_max_filesize') . PHP_EOL;"
    php -r "echo 'post_max_size: ' . ini_get('post_max_size') . PHP_EOL;"
    echo
    
    print_message $GREEN "ملفات النظام:"
    for file in ".env" "composer.json" "public/.htaccess"; do
        if [ -f "$file" ]; then
            print_message $GREEN "✓ $file"
        else
            print_message $RED "✗ $file"
        fi
    done
    echo
    
    print_message $GREEN "مجلدات النظام:"
    for dir in "config" "database" "public" "src" "storage" "views"; do
        if [ -d "$dir" ]; then
            if [ -w "$dir" ]; then
                print_message $GREEN "✓ $dir (قابل للكتابة)"
            else
                print_message $YELLOW "✓ $dir (للقراءة فقط)"
            fi
        else
            print_message $RED "✗ $dir"
        fi
    done
    echo
}

# إنشاء ملف .env
create_env() {
    echo
    print_message $BLUE "إنشاء ملف .env..."
    echo
    
    if [ -f ".env" ]; then
        print_message $YELLOW "ملف .env موجود مسبقاً"
        read -p "هل تريد استبداله؟ (y/n): " overwrite
        if [ "$overwrite" != "y" ] && [ "$overwrite" != "Y" ]; then
            return
        fi
    fi
    
    if [ -f ".env.example" ]; then
        cp ".env.example" ".env"
        print_message $GREEN "تم إنشاء ملف .env من .env.example"
    else
        cat > .env << EOF
# إعدادات التطبيق
APP_NAME=LYstoreE
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost:8000
APP_KEY=$(openssl rand -base64 32)

# إعدادات قاعدة البيانات
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=lystore
DB_USERNAME=root
DB_PASSWORD=

# إعدادات الشركة
COMPANY_NAME="شركة LYstore للتجارة العامة"
COMPANY_ADDRESS="طرابلس، ليبيا"
COMPANY_PHONE="0911111111"
COMPANY_EMAIL="<EMAIL>"

# إعدادات النظام
DEFAULT_CURRENCY="د.ل"
DEFAULT_TAX_RATE=14.00
LOW_STOCK_ALERT=10
EOF
        print_message $GREEN "تم إنشاء ملف .env جديد"
    fi
    
    echo
    print_message $YELLOW "يمكنك الآن تعديل ملف .env لتخصيص الإعدادات"
    read -p "هل تريد فتح ملف .env للتعديل؟ (y/n): " edit
    if [ "$edit" = "y" ] || [ "$edit" = "Y" ]; then
        ${EDITOR:-nano} .env
    fi
}

# تعيين الصلاحيات
set_permissions() {
    echo
    print_message $BLUE "تعيين الصلاحيات..."
    echo
    
    chmod -R 755 storage/
    chmod -R 755 public/uploads/
    
    # محاولة تغيير المالك إذا كان المستخدم root
    if [ "$EUID" -eq 0 ]; then
        chown -R www-data:www-data storage/ 2>/dev/null || true
        chown -R www-data:www-data public/uploads/ 2>/dev/null || true
    fi
    
    print_message $GREEN "تم تعيين الصلاحيات بنجاح"
}

# الحلقة الرئيسية
while true; do
    show_menu
    read -p "أدخل اختيارك (1-6): " choice
    
    case $choice in
        1)
            start_server
            ;;
        2)
            open_installer
            ;;
        3)
            show_system_info
            read -p "اضغط Enter للمتابعة..."
            ;;
        4)
            create_env
            read -p "اضغط Enter للمتابعة..."
            ;;
        5)
            set_permissions
            read -p "اضغط Enter للمتابعة..."
            ;;
        6)
            echo
            print_message $GREEN "شكراً لاستخدام نظام LYstoreE!"
            echo
            exit 0
            ;;
        *)
            print_message $RED "اختيار غير صحيح!"
            ;;
    esac
    
    echo
done

<?php

namespace LYstore\Controllers;

use LYstore\Core\Controller;
use LYstore\Models\User;

/**
 * متحكم المصادقة
 */
class AuthController extends Controller
{
    private $userModel;

    public function __construct()
    {
        parent::__construct();
        $this->userModel = new User();
    }

    /**
     * عرض صفحة تسجيل الدخول
     */
    public function showLogin(): void
    {
        // إذا كان المستخدم مسجل دخول، إعادة توجيه للوحة التحكم
        if ($this->session->isLoggedIn()) {
            $this->redirect('/dashboard');
            return;
        }

        $this->view('auth.login', [
            'title' => 'تسجيل الدخول',
            'csrf_token' => $this->session->generateCsrfToken()
        ]);
    }

    /**
     * معالجة تسجيل الدخول
     */
    public function login(): void
    {
        try {
            // التحقق من CSRF Token
            if (!$this->request->validateCsrfToken()) {
                $this->json(['error' => 'رمز الأمان غير صحيح'], 403);
                return;
            }

            // التحقق من صحة البيانات
            $validation = $this->validate($this->request->all(), [
                'username' => 'required|min:3',
                'password' => 'required|min:6'
            ]);

            if (!$validation['valid']) {
                $this->json([
                    'success' => false,
                    'errors' => $validation['errors']
                ], 422);
                return;
            }

            $username = $this->request->sanitize('username');
            $password = $this->request->get('password');

            // البحث عن المستخدم
            $user = $this->userModel->findByUsername($username);

            if (!$user) {
                $this->json([
                    'success' => false,
                    'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'
                ], 401);
                return;
            }

            // التحقق من كلمة المرور
            if (!$this->userModel->verifyPassword($password, $user['password_hash'])) {
                $this->json([
                    'success' => false,
                    'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'
                ], 401);
                return;
            }

            // التحقق من حالة المستخدم
            if ($user['status'] !== 'active') {
                $this->json([
                    'success' => false,
                    'message' => 'حسابك غير مفعل، يرجى التواصل مع المدير'
                ], 403);
                return;
            }

            // تسجيل الدخول
            $this->session->login($user);

            // تحديث آخر تسجيل دخول
            $this->userModel->updateLastLogin($user['user_id']);

            // تسجيل العملية
            $this->logActivity('تسجيل دخول', 'users', $user['user_id']);

            // إرسال استجابة نجاح
            $this->json([
                'success' => true,
                'message' => 'تم تسجيل الدخول بنجاح',
                'redirect' => '/dashboard',
                'user' => [
                    'name' => $user['name'],
                    'role' => $user['role_name']
                ]
            ]);

        } catch (\Exception $e) {
            $this->handleError($e, 'حدث خطأ أثناء تسجيل الدخول');
        }
    }

    /**
     * تسجيل الخروج
     */
    public function logout(): void
    {
        try {
            $userId = $this->session->getUserId();
            
            // تسجيل العملية
            if ($userId) {
                $this->logActivity('تسجيل خروج', 'users', $userId);
            }

            // تسجيل الخروج
            $this->session->logout();

            // إرسال استجابة أو إعادة توجيه
            if ($this->request->isAjax()) {
                $this->json([
                    'success' => true,
                    'message' => 'تم تسجيل الخروج بنجاح',
                    'redirect' => '/login'
                ]);
            } else {
                $this->redirectWithMessage('/login', 'تم تسجيل الخروج بنجاح', 'success');
            }

        } catch (\Exception $e) {
            $this->handleError($e, 'حدث خطأ أثناء تسجيل الخروج');
        }
    }

    /**
     * عرض صفحة تغيير كلمة المرور
     */
    public function showChangePassword(): void
    {
        $this->requireAuth();
        
        $this->view('auth.change-password', [
            'title' => 'تغيير كلمة المرور',
            'csrf_token' => $this->session->generateCsrfToken()
        ]);
    }

    /**
     * تغيير كلمة المرور
     */
    public function changePassword(): void
    {
        $this->requireAuth();

        try {
            // التحقق من CSRF Token
            if (!$this->request->validateCsrfToken()) {
                $this->json(['error' => 'رمز الأمان غير صحيح'], 403);
                return;
            }

            // التحقق من صحة البيانات
            $validation = $this->validate($this->request->all(), [
                'current_password' => 'required',
                'new_password' => 'required|min:6',
                'confirm_password' => 'required'
            ]);

            if (!$validation['valid']) {
                $this->json([
                    'success' => false,
                    'errors' => $validation['errors']
                ], 422);
                return;
            }

            $currentPassword = $this->request->get('current_password');
            $newPassword = $this->request->get('new_password');
            $confirmPassword = $this->request->get('confirm_password');

            // التحقق من تطابق كلمة المرور الجديدة
            if ($newPassword !== $confirmPassword) {
                $this->json([
                    'success' => false,
                    'message' => 'كلمة المرور الجديدة غير متطابقة'
                ], 422);
                return;
            }

            $userId = $this->session->getUserId();
            $user = $this->userModel->find($userId);

            // التحقق من كلمة المرور الحالية
            if (!$this->userModel->verifyPassword($currentPassword, $user['password_hash'])) {
                $this->json([
                    'success' => false,
                    'message' => 'كلمة المرور الحالية غير صحيحة'
                ], 401);
                return;
            }

            // تحديث كلمة المرور
            $this->userModel->updatePassword($userId, $newPassword);

            // تسجيل العملية
            $this->logActivity('تغيير كلمة المرور', 'users', $userId);

            $this->json([
                'success' => true,
                'message' => 'تم تغيير كلمة المرور بنجاح'
            ]);

        } catch (\Exception $e) {
            $this->handleError($e, 'حدث خطأ أثناء تغيير كلمة المرور');
        }
    }

    /**
     * التحقق من حالة تسجيل الدخول
     */
    public function checkAuth(): void
    {
        $this->json([
            'logged_in' => $this->session->isLoggedIn(),
            'user' => $this->session->isLoggedIn() ? [
                'id' => $this->session->getUserId(),
                'name' => $this->session->getUserName(),
                'role' => $this->session->getUserRole()
            ] : null
        ]);
    }
}

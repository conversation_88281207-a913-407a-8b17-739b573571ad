# نظام LYstoreE لإدارة المبيعات والمشتريات والمخازن

## نظرة عامة
نظام LYstoreE هو منصة ويب متكاملة تهدف إلى إدارة جميع عمليات المبيعات، المشتريات، المخازن، والمالية، مع دعم التحليل اللحظي واتخاذ القرار بناءً على بيانات دقيقة.

تم تصميم النظام خصيصًا للشركات في مجالات التجارة العامة، بيع الجملة والتجزئة، والاستيراد والتصدير، مع واجهة استخدام عربية بالكامل تدعم الدينار الليبي (د.ل) وتراعي أسلوب العمل المحلي في ليبيا.

## الأهداف الرئيسية
- أتمتة سير العمل بين الأقسام (المبيعات، المشتريات، المخازن، المالية)
- تحسين دقة البيانات وسرعة الوصول للمعلومات
- تقليل الأخطاء البشرية وزيادة كفاءة العمليات
- توفير تقارير تحليلية تساعد على اتخاذ قرارات استراتيجية

## الوحدات الرئيسية

### 1. وحدة إدارة المبيعات
- إدارة العملاء وتصنيفهم
- إدارة المنتجات والخدمات مع التسعير المتعدد
- عروض الأسعار والمتابعة
- أوامر البيع والفواتير
- المدفوعات والمرتجعات

### 2. وحدة إدارة المشتريات
- إدارة الموردين وشروط الدفع
- أوامر الشراء والمتابعة
- فواتير المشتريات والمدفوعات
- مرتجعات المشتريات

### 3. وحدة إدارة المخازن
- إدارة الأصناف مع دعم الباركود
- إدارة المستودعات المتعددة
- حركة المخزون والتحويلات
- الجرد وتنبيهات المخزون

### 4. وحدة التقارير والتحليلات
- تقارير مبيعات ومشتريات مفصلة
- تقارير مخزون وحركة الأصناف
- تقارير مالية شاملة
- Dashboard تفاعلي مع مؤشرات الأداء

## المتطلبات التقنية

### Backend
- **PHP**: 8.x أو أحدث
- **Framework**: إطار عمل مخصص مبني على أفضل الممارسات
- **Database**: MySQL 8.x
- **Security**: تشفير كلمات المرور، حماية من SQL Injection وXSS

### Frontend
- **HTML5** + **CSS3**
- **Bootstrap 5 RTL** للتصميم المتجاوب
- **JavaScript/jQuery** + **AJAX** للتفاعل
- **واجهة عربية** بالكامل

### الأمان والنسخ الاحتياطي
- كلمات مرور مشفرة (Bcrypt/Argon2)
- استعلامات مُهيّأة لمنع SQL Injection
- حماية XSS وCSRF
- نسخ احتياطي يومي تلقائي

## صلاحيات المستخدمين
- **مدير النظام**: وصول كامل لجميع الوحدات
- **مدير قسم**: وصول كامل لوحدة معينة
- **موظف تنفيذي**: وصول جزئي لتنفيذ مهامه
- **مدقق/مشاهد**: وصول للقراءة فقط

## الميزات الإضافية
- دعم التكامل مع أنظمة نقاط البيع POS
- دعم العمل دون اتصال (Offline Mode)
- واجهة API للربط مع تطبيقات أخرى
- دعم تعدد العملات مع التحويل للدينار الليبي
- سجل نشاط المستخدمين (Audit Log)

## هيكل المشروع
```
LYstoreE/
├── config/              # ملفات الإعدادات
├── database/            # قاعدة البيانات والهجرات
├── install/             # ملفات التثبيت
├── public/              # الملفات العامة (CSS, JS, Images)
├── routes/              # ملفات المسارات
├── src/                 # الكود المصدري
│   ├── Controllers/     # المتحكمات
│   ├── Core/            # النواة الأساسية
│   ├── Middlewares/     # الوسطاء
│   ├── Models/          # النماذج
│   └── Services/        # الخدمات
├── storage/             # ملفات التخزين
├── tests/               # الاختبارات
├── views/               # ملفات العرض
└── vendor/              # المكتبات الخارجية
```

## التثبيت والإعداد

### المتطلبات الأساسية
- PHP 8.0 أو أحدث
- MySQL 8.0 أو أحدث
- خادم ويب (Apache/Nginx)
- Composer (اختياري)

### خطوات التثبيت

1. **تحميل الملفات**
   ```bash
   git clone https://github.com/your-repo/LYstoreE.git
   cd LYstoreE
   ```

2. **إعداد قاعدة البيانات**
   - إنشاء قاعدة بيانات جديدة
   - تحديث إعدادات الاتصال في `.env`

3. **تشغيل معالج التثبيت**
   - افتح المتصفح وانتقل إلى `http://your-domain/install/install.php`
   - اتبع التعليمات لإكمال التثبيت

4. **إعداد الصلاحيات**
   ```bash
   chmod -R 755 storage/
   chmod -R 755 public/uploads/
   ```

### الإعدادات الأساسية

#### ملف .env
```env
# إعدادات التطبيق
APP_NAME=LYstoreE
APP_ENV=production
APP_DEBUG=false
APP_URL=http://localhost

# إعدادات قاعدة البيانات
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=lystore
DB_USERNAME=root
DB_PASSWORD=

# إعدادات الشركة
COMPANY_NAME="شركة LYstore للتجارة العامة"
COMPANY_ADDRESS="طرابلس، ليبيا"
COMPANY_PHONE="0911111111"
COMPANY_EMAIL="<EMAIL>"
```

## الاستخدام

### تسجيل الدخول الأول
- اسم المستخدم: `admin`
- كلمة المرور: `123456` (يُنصح بتغييرها فوراً)

### الوظائف الأساسية

#### إدارة العملاء
1. انتقل إلى قسم "العملاء"
2. اضغط "إضافة عميل جديد"
3. املأ البيانات المطلوبة
4. احفظ البيانات

#### إنشاء فاتورة بيع
1. انتقل إلى "أوامر البيع"
2. اضغط "إنشاء أمر جديد"
3. اختر العميل والمنتجات
4. احفظ الأمر وحوله إلى فاتورة

#### إدارة المخزون
1. انتقل إلى "المنتجات"
2. راجع مستويات المخزون
3. استخدم "حركة المخزون" لتحديث الكميات

## الأمان

### أفضل الممارسات
- تغيير كلمات المرور الافتراضية
- تحديث النظام بانتظام
- إجراء نسخ احتياطية دورية
- مراجعة سجلات النشاط

### الحماية المدمجة
- تشفير كلمات المرور
- حماية من SQL Injection
- حماية من XSS
- التحقق من CSRF Token
- تسجيل جميع العمليات

## النسخ الاحتياطي والاستعادة

### النسخ الاحتياطي التلقائي
يقوم النظام بإنشاء نسخة احتياطية يومية تلقائياً في مجلد `storage/backups/`

### النسخ الاحتياطي اليدوي
```bash
php scripts/backup.php
```

### استعادة النسخة الاحتياطية
```bash
php scripts/restore.php backup_file.sql
```

## استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في الاتصال بقاعدة البيانات
- تحقق من إعدادات قاعدة البيانات في `.env`
- تأكد من تشغيل خادم MySQL
- تحقق من صلاحيات المستخدم

#### مشاكل الصلاحيات
```bash
chmod -R 755 storage/
chmod -R 755 public/uploads/
chown -R www-data:www-data storage/
```

#### مشاكل الذاكرة
- زيادة `memory_limit` في PHP
- تحسين استعلامات قاعدة البيانات

## التطوير والمساهمة

### بيئة التطوير
```bash
# تفعيل وضع التطوير
APP_ENV=development
APP_DEBUG=true
```

### إضافة ميزات جديدة
1. إنشاء فرع جديد
2. تطوير الميزة
3. كتابة الاختبارات
4. إرسال طلب دمج

### معايير الكود
- اتباع PSR-12 لـ PHP
- استخدام التعليقات العربية
- كتابة اختبارات للوظائف الجديدة

## الدعم والمساعدة

### التوثيق
- [دليل المستخدم](docs/user-guide.md)
- [دليل المطور](docs/developer-guide.md)
- [API Documentation](docs/api.md)

### الدعم الفني
- البريد الإلكتروني: <EMAIL>
- الهاتف: +218-91-111-1111
- الموقع: https://lystore.ly

## الترخيص
هذا المشروع مطور خصيصاً لاحتياجات السوق الليبي ومحمي بحقوق الطبع والنشر.

## سجل التغييرات

### الإصدار 1.0.0 (2025-01-11)
- إطلاق النسخة الأولى
- وحدات المبيعات والمشتريات والمخازن
- واجهة مستخدم عربية كاملة
- نظام صلاحيات متقدم

---
**تم التطوير بواسطة**: فريق تطوير LYstoreE
**التاريخ**: يناير 2025
**الإصدار**: 1.0.0

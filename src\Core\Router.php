<?php

namespace LYstore\Core;

/**
 * فئة توجيه الطلبات
 */
class Router
{
    private $routes = [];
    private $middlewares = [];

    /**
     * إضافة مسار GET
     */
    public function get(string $path, $handler, array $middlewares = []): void
    {
        $this->addRoute('GET', $path, $handler, $middlewares);
    }

    /**
     * إضافة مسار POST
     */
    public function post(string $path, $handler, array $middlewares = []): void
    {
        $this->addRoute('POST', $path, $handler, $middlewares);
    }

    /**
     * إضافة مسار PUT
     */
    public function put(string $path, $handler, array $middlewares = []): void
    {
        $this->addRoute('PUT', $path, $handler, $middlewares);
    }

    /**
     * إضافة مسار DELETE
     */
    public function delete(string $path, $handler, array $middlewares = []): void
    {
        $this->addRoute('DELETE', $path, $handler, $middlewares);
    }

    /**
     * إضافة مسار لجميع الطرق
     */
    public function any(string $path, $handler, array $middlewares = []): void
    {
        $methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
        foreach ($methods as $method) {
            $this->addRoute($method, $path, $handler, $middlewares);
        }
    }

    /**
     * إضافة مسار
     */
    private function addRoute(string $method, string $path, $handler, array $middlewares = []): void
    {
        $this->routes[] = [
            'method' => $method,
            'path' => $path,
            'handler' => $handler,
            'middlewares' => $middlewares,
            'pattern' => $this->convertToPattern($path)
        ];
    }

    /**
     * تحويل المسار إلى نمط تعبير نمطي
     */
    private function convertToPattern(string $path): string
    {
        // تحويل المعاملات مثل {id} إلى تعبير نمطي
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $path);
        return '#^' . $pattern . '$#';
    }

    /**
     * معالجة الطلب
     */
    public function dispatch(Request $request, Response $response): void
    {
        $method = $request->method();
        $path = $request->path();
        
        foreach ($this->routes as $route) {
            if ($route['method'] === $method && preg_match($route['pattern'], $path, $matches)) {
                // إزالة المطابقة الكاملة
                array_shift($matches);
                
                // تنفيذ الوسطاء
                foreach ($route['middlewares'] as $middleware) {
                    $this->runMiddleware($middleware, $request, $response);
                }
                
                // تنفيذ المعالج
                $this->runHandler($route['handler'], $matches, $request, $response);
                return;
            }
        }
        
        // لم يتم العثور على مسار مطابق
        $this->handleNotFound($response);
    }

    /**
     * تنفيذ معالج المسار
     */
    private function runHandler($handler, array $params, Request $request, Response $response): void
    {
        if (is_string($handler)) {
            // تنسيق: 'ControllerName@methodName'
            if (strpos($handler, '@') !== false) {
                [$controllerName, $methodName] = explode('@', $handler);
                $controllerClass = "LYstore\\Controllers\\{$controllerName}";
                
                if (class_exists($controllerClass)) {
                    $controller = new $controllerClass();
                    if (method_exists($controller, $methodName)) {
                        call_user_func_array([$controller, $methodName], $params);
                        return;
                    }
                }
            }
        } elseif (is_callable($handler)) {
            // دالة مباشرة
            call_user_func_array($handler, array_merge([$request, $response], $params));
            return;
        }
        
        // معالج غير صحيح
        $response->error('معالج المسار غير صحيح', 500);
    }

    /**
     * تنفيذ الوسطاء
     */
    private function runMiddleware(string $middleware, Request $request, Response $response): void
    {
        $middlewareClass = "LYstore\\Middlewares\\{$middleware}";
        
        if (class_exists($middlewareClass)) {
            $middlewareInstance = new $middlewareClass();
            if (method_exists($middlewareInstance, 'handle')) {
                $middlewareInstance->handle($request, $response);
            }
        }
    }

    /**
     * معالجة عدم وجود مسار
     */
    private function handleNotFound(Response $response): void
    {
        $response->setStatusCode(404);
        
        // التحقق من وجود صفحة 404 مخصصة
        $notFoundView = ROOT_PATH . '/views/errors/404.php';
        if (file_exists($notFoundView)) {
            include $notFoundView;
        } else {
            echo '<h1>404 - الصفحة غير موجودة</h1>';
            echo '<p>الصفحة التي تبحث عنها غير موجودة.</p>';
        }
    }

    /**
     * إضافة مجموعة مسارات مع بادئة
     */
    public function group(string $prefix, callable $callback, array $middlewares = []): void
    {
        $originalRoutes = $this->routes;
        
        // تنفيذ الدالة لإضافة المسارات
        $callback($this);
        
        // تحديث المسارات الجديدة بالبادئة والوسطاء
        $newRoutes = array_slice($this->routes, count($originalRoutes));
        
        foreach ($newRoutes as &$route) {
            $route['path'] = rtrim($prefix, '/') . '/' . ltrim($route['path'], '/');
            $route['pattern'] = $this->convertToPattern($route['path']);
            $route['middlewares'] = array_merge($middlewares, $route['middlewares']);
        }
        
        // استبدال المسارات الجديدة
        $this->routes = array_merge($originalRoutes, $newRoutes);
    }

    /**
     * إنشاء رابط
     */
    public function url(string $path, array $params = []): string
    {
        $url = rtrim(Autoloader::config('url', 'http://localhost'), '/') . '/' . ltrim($path, '/');
        
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        
        return $url;
    }

    /**
     * إعادة توجيه إلى مسار
     */
    public function redirectTo(string $path, array $params = []): void
    {
        $url = $this->url($path, $params);
        header("Location: {$url}");
        exit;
    }
}

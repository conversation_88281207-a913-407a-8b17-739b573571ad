<?php
/**
 * نقطة الدخول الرئيسية لنظام LYstoreE
 */

// تحديد مسار الجذر
define('ROOT_PATH', dirname(__DIR__));

// تحميل النظام الأساسي
require_once ROOT_PATH . '/src/Core/Autoloader.php';

use LYstore\Core\Autoloader;
use LYstore\Core\Router;
use LYstore\Core\Request;
use LYstore\Core\Response;

try {
    // تهيئة النظام
    Autoloader::bootstrap();
    
    // إنشاء كائنات الطلب والاستجابة
    $request = new Request();
    $response = new Response();
    
    // إنشاء الموجه
    $router = new Router();
    
    // تحميل المسارات
    require_once ROOT_PATH . '/routes/web.php';
    
    // معالجة الطلب
    $router->dispatch($request, $response);
    
} catch (Exception $e) {
    // معالجة الأخطاء العامة
    error_log($e->getMessage());
    
    if (Autoloader::config('debug', false)) {
        echo '<h1>خطأ في النظام</h1>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
    } else {
        echo '<h1>عذراً، حدث خطأ غير متوقع</h1>';
        echo '<p>يرجى المحاولة مرة أخرى لاحقاً</p>';
    }
}

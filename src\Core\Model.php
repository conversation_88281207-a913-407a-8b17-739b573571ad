<?php

namespace LYstore\Core;

/**
 * النموذج الأساسي لجميع النماذج في النظام
 */
abstract class Model
{
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];
    protected $hidden = [];
    protected $timestamps = true;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * البحث عن جميع السجلات
     */
    public function all(): array
    {
        $query = "SELECT * FROM {$this->table}";
        return $this->db->select($query);
    }

    /**
     * البحث عن سجل بواسطة المعرف
     */
    public function find(int $id): ?array
    {
        $query = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = :id";
        return $this->db->selectOne($query, ['id' => $id]);
    }

    /**
     * البحث عن سجل بشرط معين
     */
    public function findWhere(string $column, $value): ?array
    {
        $query = "SELECT * FROM {$this->table} WHERE {$column} = :value";
        return $this->db->selectOne($query, ['value' => $value]);
    }

    /**
     * البحث عن عدة سجلات بشرط معين
     */
    public function where(string $column, $value): array
    {
        $query = "SELECT * FROM {$this->table} WHERE {$column} = :value";
        return $this->db->select($query, ['value' => $value]);
    }

    /**
     * إنشاء سجل جديد
     */
    public function create(array $data): int
    {
        // تصفية البيانات المسموحة فقط
        $filteredData = $this->filterFillable($data);
        
        // إضافة الطوابع الزمنية إذا كانت مفعلة
        if ($this->timestamps) {
            $filteredData['created_at'] = date('Y-m-d H:i:s');
            $filteredData['updated_at'] = date('Y-m-d H:i:s');
        }

        return $this->db->insert($this->table, $filteredData);
    }

    /**
     * تحديث سجل موجود
     */
    public function update(int $id, array $data): int
    {
        // تصفية البيانات المسموحة فقط
        $filteredData = $this->filterFillable($data);
        
        // إضافة طابع زمني للتحديث إذا كان مفعلاً
        if ($this->timestamps) {
            $filteredData['updated_at'] = date('Y-m-d H:i:s');
        }

        return $this->db->update(
            $this->table,
            $filteredData,
            "{$this->primaryKey} = :id",
            ['id' => $id]
        );
    }

    /**
     * حذف سجل
     */
    public function delete(int $id): int
    {
        return $this->db->delete(
            $this->table,
            "{$this->primaryKey} = :id",
            ['id' => $id]
        );
    }

    /**
     * البحث مع ترقيم الصفحات
     */
    public function paginate(int $page = 1, int $perPage = 15, string $where = '1=1', array $params = []): array
    {
        $offset = ($page - 1) * $perPage;
        
        // الحصول على إجمالي عدد السجلات
        $totalQuery = "SELECT COUNT(*) as total FROM {$this->table} WHERE {$where}";
        $totalResult = $this->db->selectOne($totalQuery, $params);
        $total = (int) $totalResult['total'];
        
        // الحصول على السجلات للصفحة الحالية
        $dataQuery = "SELECT * FROM {$this->table} WHERE {$where} LIMIT {$perPage} OFFSET {$offset}";
        $data = $this->db->select($dataQuery, $params);
        
        return [
            'data' => $data,
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $page,
            'last_page' => ceil($total / $perPage),
            'from' => $offset + 1,
            'to' => min($offset + $perPage, $total)
        ];
    }

    /**
     * البحث بشروط متقدمة
     */
    public function search(array $conditions = [], string $orderBy = '', int $limit = 0): array
    {
        $where = [];
        $params = [];
        
        foreach ($conditions as $column => $value) {
            if (is_array($value)) {
                // للبحث في نطاق (BETWEEN)
                if (isset($value['from']) && isset($value['to'])) {
                    $where[] = "{$column} BETWEEN :from_{$column} AND :to_{$column}";
                    $params["from_{$column}"] = $value['from'];
                    $params["to_{$column}"] = $value['to'];
                }
                // للبحث في قائمة (IN)
                elseif (isset($value['in'])) {
                    $placeholders = [];
                    foreach ($value['in'] as $i => $item) {
                        $placeholder = "in_{$column}_{$i}";
                        $placeholders[] = ":{$placeholder}";
                        $params[$placeholder] = $item;
                    }
                    $where[] = "{$column} IN (" . implode(',', $placeholders) . ")";
                }
            } else {
                $where[] = "{$column} = :{$column}";
                $params[$column] = $value;
            }
        }
        
        $whereClause = empty($where) ? '1=1' : implode(' AND ', $where);
        $query = "SELECT * FROM {$this->table} WHERE {$whereClause}";
        
        if (!empty($orderBy)) {
            $query .= " ORDER BY {$orderBy}";
        }
        
        if ($limit > 0) {
            $query .= " LIMIT {$limit}";
        }
        
        return $this->db->select($query, $params);
    }

    /**
     * تصفية البيانات المسموحة فقط
     */
    protected function filterFillable(array $data): array
    {
        if (empty($this->fillable)) {
            return $data;
        }
        
        return array_intersect_key($data, array_flip($this->fillable));
    }

    /**
     * إخفاء الحقول المحددة من النتائج
     */
    protected function hideFields(array $data): array
    {
        if (empty($this->hidden)) {
            return $data;
        }
        
        return array_diff_key($data, array_flip($this->hidden));
    }

    /**
     * التحقق من وجود سجل
     */
    public function exists(int $id): bool
    {
        return $this->db->exists($this->table, "{$this->primaryKey} = :id", ['id' => $id]);
    }

    /**
     * عد السجلات
     */
    public function count(string $where = '1=1', array $params = []): int
    {
        return $this->db->count($this->table, $where, $params);
    }

    /**
     * الحصول على أول سجل
     */
    public function first(string $orderBy = ''): ?array
    {
        $query = "SELECT * FROM {$this->table}";
        
        if (!empty($orderBy)) {
            $query .= " ORDER BY {$orderBy}";
        }
        
        $query .= " LIMIT 1";
        
        return $this->db->selectOne($query);
    }

    /**
     * الحصول على آخر سجل
     */
    public function latest(string $column = 'created_at'): ?array
    {
        return $this->first("{$column} DESC");
    }
}

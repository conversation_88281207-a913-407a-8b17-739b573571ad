<?php

namespace LYstore\Core;

use PDO;
use PDOException;

/**
 * فئة إدارة قاعدة البيانات
 */
class Database
{
    private static $instance = null;
    private $connection;
    private $config;

    private function __construct()
    {
        $this->config = require_once __DIR__ . '/../../config/database.php';
        $this->connect();
    }

    /**
     * الحصول على مثيل واحد من قاعدة البيانات (Singleton Pattern)
     */
    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * الاتصال بقاعدة البيانات
     */
    private function connect(): void
    {
        try {
            $config = $this->config['connections']['mysql'];
            
            $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
            
            $this->connection = new PDO(
                $dsn,
                $config['username'],
                $config['password'],
                $config['options']
            );
            
        } catch (PDOException $e) {
            throw new \Exception("فشل الاتصال بقاعدة البيانات: " . $e->getMessage());
        }
    }

    /**
     * الحصول على اتصال قاعدة البيانات
     */
    public function getConnection(): PDO
    {
        return $this->connection;
    }

    /**
     * تنفيذ استعلام SELECT
     */
    public function select(string $query, array $params = []): array
    {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            throw new \Exception("خطأ في تنفيذ الاستعلام: " . $e->getMessage());
        }
    }

    /**
     * تنفيذ استعلام INSERT
     */
    public function insert(string $table, array $data): int
    {
        try {
            $columns = implode(',', array_keys($data));
            $placeholders = ':' . implode(', :', array_keys($data));
            
            $query = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
            
            $stmt = $this->connection->prepare($query);
            $stmt->execute($data);
            
            return $this->connection->lastInsertId();
        } catch (PDOException $e) {
            throw new \Exception("خطأ في إدراج البيانات: " . $e->getMessage());
        }
    }

    /**
     * تنفيذ استعلام UPDATE
     */
    public function update(string $table, array $data, string $where, array $whereParams = []): int
    {
        try {
            $setClause = [];
            foreach (array_keys($data) as $column) {
                $setClause[] = "{$column} = :{$column}";
            }
            $setClause = implode(', ', $setClause);
            
            $query = "UPDATE {$table} SET {$setClause} WHERE {$where}";
            
            $params = array_merge($data, $whereParams);
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            
            return $stmt->rowCount();
        } catch (PDOException $e) {
            throw new \Exception("خطأ في تحديث البيانات: " . $e->getMessage());
        }
    }

    /**
     * تنفيذ استعلام DELETE
     */
    public function delete(string $table, string $where, array $params = []): int
    {
        try {
            $query = "DELETE FROM {$table} WHERE {$where}";
            
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            
            return $stmt->rowCount();
        } catch (PDOException $e) {
            throw new \Exception("خطأ في حذف البيانات: " . $e->getMessage());
        }
    }

    /**
     * بدء معاملة قاعدة البيانات
     */
    public function beginTransaction(): bool
    {
        return $this->connection->beginTransaction();
    }

    /**
     * تأكيد المعاملة
     */
    public function commit(): bool
    {
        return $this->connection->commit();
    }

    /**
     * إلغاء المعاملة
     */
    public function rollback(): bool
    {
        return $this->connection->rollback();
    }

    /**
     * تنفيذ استعلام مخصص
     */
    public function query(string $query, array $params = []): \PDOStatement
    {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            throw new \Exception("خطأ في تنفيذ الاستعلام: " . $e->getMessage());
        }
    }

    /**
     * الحصول على سجل واحد
     */
    public function selectOne(string $query, array $params = []): ?array
    {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            $result = $stmt->fetch();
            return $result ?: null;
        } catch (PDOException $e) {
            throw new \Exception("خطأ في تنفيذ الاستعلام: " . $e->getMessage());
        }
    }

    /**
     * التحقق من وجود سجل
     */
    public function exists(string $table, string $where, array $params = []): bool
    {
        $query = "SELECT 1 FROM {$table} WHERE {$where} LIMIT 1";
        $result = $this->selectOne($query, $params);
        return $result !== null;
    }

    /**
     * عد السجلات
     */
    public function count(string $table, string $where = '1=1', array $params = []): int
    {
        $query = "SELECT COUNT(*) as count FROM {$table} WHERE {$where}";
        $result = $this->selectOne($query, $params);
        return (int) $result['count'];
    }
}

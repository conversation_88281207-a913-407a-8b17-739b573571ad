<?php
/**
 * ملف بدء تشغيل سريع لنظام LYstoreE
 * يمكن استخدامه لتشغيل النظام بسرعة للاختبار
 */

// التحقق من إصدار PHP
if (version_compare(PHP_VERSION, '8.0.0', '<')) {
    die('يتطلب النظام PHP 8.0 أو أحدث. الإصدار الحالي: ' . PHP_VERSION);
}

// التحقق من الامتدادات المطلوبة
$requiredExtensions = ['pdo', 'pdo_mysql', 'mbstring', 'json', 'openssl'];
$missingExtensions = [];

foreach ($requiredExtensions as $extension) {
    if (!extension_loaded($extension)) {
        $missingExtensions[] = $extension;
    }
}

if (!empty($missingExtensions)) {
    die('امتدادات PHP مطلوبة غير مثبتة: ' . implode(', ', $missingExtensions));
}

// التحقق من وجود ملف .env
if (!file_exists('.env')) {
    echo "ملف .env غير موجود. سيتم إنشاؤه الآن...\n";
    
    if (file_exists('.env.example')) {
        copy('.env.example', '.env');
        echo "تم إنشاء ملف .env من .env.example\n";
    } else {
        // إنشاء ملف .env أساسي
        $envContent = "# إعدادات التطبيق
APP_NAME=LYstoreE
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost:8000
APP_KEY=" . base64_encode(random_bytes(32)) . "

# إعدادات قاعدة البيانات
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=lystore
DB_USERNAME=root
DB_PASSWORD=

# إعدادات الشركة
COMPANY_NAME=\"شركة LYstore للتجارة العامة\"
COMPANY_ADDRESS=\"طرابلس، ليبيا\"
COMPANY_PHONE=\"0911111111\"
COMPANY_EMAIL=\"<EMAIL>\"

# إعدادات النظام
DEFAULT_CURRENCY=\"د.ل\"
DEFAULT_TAX_RATE=14.00
LOW_STOCK_ALERT=10
";
        
        file_put_contents('.env', $envContent);
        echo "تم إنشاء ملف .env جديد\n";
    }
}

// التحقق من المجلدات المطلوبة
$requiredDirs = [
    'storage/logs',
    'storage/sessions', 
    'storage/backups',
    'storage/cache',
    'public/uploads'
];

foreach ($requiredDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "تم إنشاء المجلد: $dir\n";
    }
}

// التحقق من صلاحيات الكتابة
$writableDirs = ['storage', 'public/uploads'];
foreach ($writableDirs as $dir) {
    if (!is_writable($dir)) {
        echo "تحذير: المجلد $dir غير قابل للكتابة\n";
    }
}

echo "\n=== نظام LYstoreE ===\n";
echo "تم التحقق من المتطلبات بنجاح!\n\n";

// عرض خيارات التشغيل
echo "اختر أحد الخيارات التالية:\n";
echo "1. تشغيل خادم التطوير المدمج\n";
echo "2. تثبيت قاعدة البيانات\n";
echo "3. إنشاء نسخة احتياطية\n";
echo "4. عرض معلومات النظام\n";
echo "5. الخروج\n\n";

$choice = readline("أدخل اختيارك (1-5): ");

switch ($choice) {
    case '1':
        startDevelopmentServer();
        break;
    case '2':
        installDatabase();
        break;
    case '3':
        createBackup();
        break;
    case '4':
        showSystemInfo();
        break;
    case '5':
        echo "شكراً لاستخدام نظام LYstoreE!\n";
        exit(0);
    default:
        echo "اختيار غير صحيح!\n";
        break;
}

/**
 * تشغيل خادم التطوير المدمج
 */
function startDevelopmentServer() {
    $host = '127.0.0.1';
    $port = 8000;
    $docRoot = __DIR__ . '/public';
    
    echo "بدء تشغيل خادم التطوير...\n";
    echo "الرابط: http://{$host}:{$port}\n";
    echo "اضغط Ctrl+C لإيقاف الخادم\n\n";
    
    // التحقق من توفر المنفذ
    $socket = @fsockopen($host, $port, $errno, $errstr, 1);
    if ($socket) {
        fclose($socket);
        echo "تحذير: المنفذ {$port} مستخدم مسبقاً\n";
        $port = findAvailablePort($host, $port + 1);
        echo "سيتم استخدام المنفذ: {$port}\n";
    }
    
    $command = "php -S {$host}:{$port} -t {$docRoot}";
    
    // فتح المتصفح تلقائياً (Windows)
    if (PHP_OS_FAMILY === 'Windows') {
        exec("start http://{$host}:{$port}");
    }
    
    passthru($command);
}

/**
 * البحث عن منفذ متاح
 */
function findAvailablePort($host, $startPort) {
    for ($port = $startPort; $port <= $startPort + 100; $port++) {
        $socket = @fsockopen($host, $port, $errno, $errstr, 1);
        if (!$socket) {
            return $port;
        }
        fclose($socket);
    }
    return $startPort;
}

/**
 * تثبيت قاعدة البيانات
 */
function installDatabase() {
    echo "تثبيت قاعدة البيانات...\n";
    
    // التحقق من وجود ملف التثبيت
    if (file_exists('install/install.php')) {
        echo "يمكنك تثبيت قاعدة البيانات من خلال:\n";
        echo "1. المتصفح: http://localhost:8000/install/install.php\n";
        echo "2. سطر الأوامر: php install/install.php\n";
    } else {
        echo "ملف التثبيت غير موجود!\n";
    }
}

/**
 * إنشاء نسخة احتياطية
 */
function createBackup() {
    echo "إنشاء نسخة احتياطية...\n";
    
    // التحقق من وجود ملف النسخ الاحتياطي
    if (file_exists('scripts/backup.php')) {
        include 'scripts/backup.php';
    } else {
        echo "ملف النسخ الاحتياطي غير موجود!\n";
        echo "يمكنك إنشاء نسخة احتياطية يدوياً من قاعدة البيانات\n";
    }
}

/**
 * عرض معلومات النظام
 */
function showSystemInfo() {
    echo "\n=== معلومات النظام ===\n";
    echo "إصدار PHP: " . PHP_VERSION . "\n";
    echo "نظام التشغيل: " . PHP_OS . "\n";
    echo "الذاكرة المتاحة: " . ini_get('memory_limit') . "\n";
    echo "الحد الأقصى لرفع الملفات: " . ini_get('upload_max_filesize') . "\n";
    echo "الحد الأقصى لحجم POST: " . ini_get('post_max_size') . "\n";
    
    echo "\n=== الامتدادات المثبتة ===\n";
    $extensions = ['pdo', 'pdo_mysql', 'mbstring', 'json', 'openssl', 'gd', 'curl'];
    foreach ($extensions as $ext) {
        $status = extension_loaded($ext) ? '✓' : '✗';
        echo "{$status} {$ext}\n";
    }
    
    echo "\n=== مجلدات النظام ===\n";
    $dirs = ['config', 'database', 'public', 'src', 'storage', 'views'];
    foreach ($dirs as $dir) {
        $status = is_dir($dir) ? '✓' : '✗';
        $writable = is_writable($dir) ? '(قابل للكتابة)' : '(للقراءة فقط)';
        echo "{$status} {$dir} {$writable}\n";
    }
    
    echo "\n=== ملفات الإعداد ===\n";
    $files = ['.env', 'composer.json', 'public/.htaccess'];
    foreach ($files as $file) {
        $status = file_exists($file) ? '✓' : '✗';
        echo "{$status} {$file}\n";
    }
    
    echo "\n";
}

echo "\nتم الانتهاء!\n";
?>

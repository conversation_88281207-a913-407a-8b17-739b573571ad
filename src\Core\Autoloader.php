<?php

namespace LYstore\Core;

/**
 * فئة التحميل التلقائي للفئات
 */
class Autoloader
{
    private static $registered = false;
    private static $namespaces = [];

    /**
     * تسجيل التحميل التلقائي
     */
    public static function register(): void
    {
        if (self::$registered) {
            return;
        }

        spl_autoload_register([self::class, 'load']);
        self::$registered = true;

        // تسجيل مساحات الأسماء الافتراضية
        self::addNamespace('LYstore', __DIR__ . '/..');
    }

    /**
     * إضافة مساحة أسماء
     */
    public static function addNamespace(string $namespace, string $path): void
    {
        $namespace = trim($namespace, '\\') . '\\';
        $path = rtrim($path, '/') . '/';
        
        self::$namespaces[$namespace] = $path;
    }

    /**
     * تحميل الفئة
     */
    public static function load(string $className): void
    {
        foreach (self::$namespaces as $namespace => $path) {
            if (strpos($className, $namespace) === 0) {
                $relativeClass = substr($className, strlen($namespace));
                $file = $path . str_replace('\\', '/', $relativeClass) . '.php';
                
                if (file_exists($file)) {
                    require_once $file;
                    return;
                }
            }
        }
    }

    /**
     * تحميل ملف الإعدادات
     */
    public static function loadConfig(): void
    {
        // تحميل متغيرات البيئة
        self::loadEnv();
        
        // تحميل الإعدادات
        $configPath = __DIR__ . '/../../config/app.php';
        if (file_exists($configPath)) {
            $GLOBALS['config'] = require $configPath;
        }
    }

    /**
     * تحميل متغيرات البيئة من ملف .env
     */
    private static function loadEnv(): void
    {
        $envPath = __DIR__ . '/../../.env';
        
        if (!file_exists($envPath)) {
            return;
        }

        $lines = file($envPath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($lines as $line) {
            if (strpos($line, '#') === 0) {
                continue; // تجاهل التعليقات
            }
            
            if (strpos($line, '=') !== false) {
                [$key, $value] = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value, '"\'');
                
                if (!array_key_exists($key, $_ENV)) {
                    $_ENV[$key] = $value;
                }
            }
        }
    }

    /**
     * الحصول على إعداد
     */
    public static function config(string $key, $default = null)
    {
        $keys = explode('.', $key);
        $config = $GLOBALS['config'] ?? [];
        
        foreach ($keys as $segment) {
            if (is_array($config) && array_key_exists($segment, $config)) {
                $config = $config[$segment];
            } else {
                return $default;
            }
        }
        
        return $config;
    }

    /**
     * تهيئة النظام
     */
    public static function bootstrap(): void
    {
        // تسجيل التحميل التلقائي
        self::register();
        
        // تحميل الإعدادات
        self::loadConfig();
        
        // تعيين المنطقة الزمنية
        $timezone = self::config('timezone', 'Africa/Tripoli');
        date_default_timezone_set($timezone);
        
        // تعيين ترميز الأحرف
        mb_internal_encoding('UTF-8');
        
        // تعيين إعدادات PHP
        ini_set('display_errors', self::config('debug', false) ? '1' : '0');
        error_reporting(self::config('debug', false) ? E_ALL : 0);
        
        // بدء الجلسة
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // تنظيف البيانات المؤقتة المنتهية الصلاحية
        $session = new Session();
        $session->cleanExpiredTemp();
    }
}

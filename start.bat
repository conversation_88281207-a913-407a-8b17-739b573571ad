@echo off
chcp 65001 >nul
title نظام LYstoreE - بدء التشغيل

echo.
echo ========================================
echo        نظام LYstoreE للمبيعات والمشتريات
echo ========================================
echo.

REM التحقق من وجود PHP
php --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: PHP غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت PHP 8.0 أو أحدث
    pause
    exit /b 1
)

REM التحقق من إصدار PHP
for /f "tokens=2" %%i in ('php --version ^| findstr /r "^PHP"') do set PHP_VERSION=%%i
echo تم العثور على PHP %PHP_VERSION%

REM التحقق من وجود ملفات النظام
if not exist "public\index.php" (
    echo خطأ: ملفات النظام غير موجودة
    echo تأكد من وجودك في مجلد LYstoreE الصحيح
    pause
    exit /b 1
)

REM إنشاء المجلدات المطلوبة
if not exist "storage" mkdir storage
if not exist "storage\logs" mkdir storage\logs
if not exist "storage\sessions" mkdir storage\sessions
if not exist "storage\backups" mkdir storage\backups
if not exist "storage\cache" mkdir storage\cache
if not exist "public\uploads" mkdir public\uploads

echo تم التحقق من المتطلبات بنجاح!
echo.

:MENU
echo اختر أحد الخيارات التالية:
echo.
echo 1. تشغيل خادم التطوير
echo 2. فتح معالج التثبيت
echo 3. عرض معلومات النظام
echo 4. إنشاء ملف .env
echo 5. فتح المجلد في المتصفح
echo 6. الخروج
echo.

set /p choice="أدخل اختيارك (1-6): "

if "%choice%"=="1" goto START_SERVER
if "%choice%"=="2" goto INSTALL
if "%choice%"=="3" goto SYSTEM_INFO
if "%choice%"=="4" goto CREATE_ENV
if "%choice%"=="5" goto OPEN_FOLDER
if "%choice%"=="6" goto EXIT
echo اختيار غير صحيح!
goto MENU

:START_SERVER
echo.
echo بدء تشغيل خادم التطوير...
echo.

REM البحث عن منفذ متاح
set PORT=8000
:CHECK_PORT
netstat -an | find ":%PORT%" >nul
if not errorlevel 1 (
    set /a PORT+=1
    goto CHECK_PORT
)

echo سيتم تشغيل الخادم على المنفذ: %PORT%
echo الرابط: http://localhost:%PORT%
echo.
echo اضغط Ctrl+C لإيقاف الخادم
echo.

REM فتح المتصفح تلقائياً
start http://localhost:%PORT%

REM تشغيل الخادم
php -S localhost:%PORT% -t public
goto MENU

:INSTALL
echo.
echo فتح معالج التثبيت...
echo.

REM التحقق من وجود ملف التثبيت
if not exist "install\install.php" (
    echo ملف التثبيت غير موجود!
    pause
    goto MENU
)

REM تشغيل خادم مؤقت للتثبيت
set INSTALL_PORT=8001
netstat -an | find ":%INSTALL_PORT%" >nul
if not errorlevel 1 (
    set /a INSTALL_PORT+=1
)

echo سيتم فتح معالج التثبيت على: http://localhost:%INSTALL_PORT%/install/install.php
start http://localhost:%INSTALL_PORT%/install/install.php

php -S localhost:%INSTALL_PORT% -t .
goto MENU

:SYSTEM_INFO
echo.
echo ========== معلومات النظام ==========
echo.

REM معلومات PHP
echo إصدار PHP:
php --version | findstr /r "^PHP"
echo.

echo الامتدادات المثبتة:
php -m | findstr /i "pdo mysql mbstring json openssl gd curl"
echo.

echo إعدادات PHP المهمة:
php -r "echo 'memory_limit: ' . ini_get('memory_limit') . PHP_EOL;"
php -r "echo 'upload_max_filesize: ' . ini_get('upload_max_filesize') . PHP_EOL;"
php -r "echo 'post_max_size: ' . ini_get('post_max_size') . PHP_EOL;"
echo.

echo ملفات النظام:
if exist ".env" (echo ✓ .env) else (echo ✗ .env)
if exist "composer.json" (echo ✓ composer.json) else (echo ✗ composer.json)
if exist "public\.htaccess" (echo ✓ public\.htaccess) else (echo ✗ public\.htaccess)
echo.

echo مجلدات النظام:
if exist "config" (echo ✓ config) else (echo ✗ config)
if exist "database" (echo ✓ database) else (echo ✗ database)
if exist "public" (echo ✓ public) else (echo ✗ public)
if exist "src" (echo ✓ src) else (echo ✗ src)
if exist "storage" (echo ✓ storage) else (echo ✗ storage)
if exist "views" (echo ✓ views) else (echo ✗ views)
echo.

pause
goto MENU

:CREATE_ENV
echo.
echo إنشاء ملف .env...
echo.

if exist ".env" (
    echo ملف .env موجود مسبقاً
    set /p overwrite="هل تريد استبداله؟ (y/n): "
    if /i not "%overwrite%"=="y" goto MENU
)

if exist ".env.example" (
    copy ".env.example" ".env" >nul
    echo تم إنشاء ملف .env من .env.example
) else (
    echo # إعدادات التطبيق > .env
    echo APP_NAME=LYstoreE >> .env
    echo APP_ENV=development >> .env
    echo APP_DEBUG=true >> .env
    echo APP_URL=http://localhost:8000 >> .env
    echo APP_KEY= >> .env
    echo. >> .env
    echo # إعدادات قاعدة البيانات >> .env
    echo DB_CONNECTION=mysql >> .env
    echo DB_HOST=localhost >> .env
    echo DB_PORT=3306 >> .env
    echo DB_DATABASE=lystore >> .env
    echo DB_USERNAME=root >> .env
    echo DB_PASSWORD= >> .env
    echo. >> .env
    echo # إعدادات الشركة >> .env
    echo COMPANY_NAME="شركة LYstore للتجارة العامة" >> .env
    echo COMPANY_ADDRESS="طرابلس، ليبيا" >> .env
    echo COMPANY_PHONE="0911111111" >> .env
    echo COMPANY_EMAIL="<EMAIL>" >> .env
    
    echo تم إنشاء ملف .env جديد
)

echo.
echo يمكنك الآن تعديل ملف .env لتخصيص الإعدادات
set /p edit="هل تريد فتح ملف .env للتعديل؟ (y/n): "
if /i "%edit%"=="y" notepad .env

pause
goto MENU

:OPEN_FOLDER
echo.
echo فتح مجلد المشروع...
explorer .
goto MENU

:EXIT
echo.
echo شكراً لاستخدام نظام LYstoreE!
echo.
pause
exit /b 0

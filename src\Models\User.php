<?php

namespace LYstore\Models;

use LYstore\Core\Model;

/**
 * نموذج المستخدم
 */
class User extends Model
{
    protected $table = 'users';
    protected $primaryKey = 'user_id';
    
    protected $fillable = [
        'name', 'username', 'password_hash', 'email', 'phone', 'role_id', 'status'
    ];
    
    protected $hidden = [
        'password_hash'
    ];

    /**
     * البحث عن مستخدم بواسطة اسم المستخدم
     */
    public function findByUsername(string $username): ?array
    {
        $query = "
            SELECT u.*, r.role_name 
            FROM {$this->table} u 
            LEFT JOIN roles r ON u.role_id = r.role_id 
            WHERE u.username = :username AND u.status = 'active'
        ";
        
        return $this->db->selectOne($query, ['username' => $username]);
    }

    /**
     * البحث عن مستخدم بواسطة البريد الإلكتروني
     */
    public function findByEmail(string $email): ?array
    {
        $query = "
            SELECT u.*, r.role_name 
            FROM {$this->table} u 
            LEFT JOIN roles r ON u.role_id = r.role_id 
            WHERE u.email = :email AND u.status = 'active'
        ";
        
        return $this->db->selectOne($query, ['email' => $email]);
    }

    /**
     * الحصول على جميع المستخدمين مع أدوارهم
     */
    public function getAllWithRoles(): array
    {
        $query = "
            SELECT u.*, r.role_name 
            FROM {$this->table} u 
            LEFT JOIN roles r ON u.role_id = r.role_id 
            ORDER BY u.name
        ";
        
        return $this->db->select($query);
    }

    /**
     * إنشاء مستخدم جديد
     */
    public function createUser(array $data): int
    {
        // تشفير كلمة المرور
        if (isset($data['password'])) {
            $data['password_hash'] = password_hash($data['password'], PASSWORD_DEFAULT);
            unset($data['password']);
        }

        return $this->create($data);
    }

    /**
     * تحديث كلمة المرور
     */
    public function updatePassword(int $userId, string $newPassword): int
    {
        $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
        
        return $this->update($userId, ['password_hash' => $passwordHash]);
    }

    /**
     * التحقق من كلمة المرور
     */
    public function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }

    /**
     * تحديث آخر تسجيل دخول
     */
    public function updateLastLogin(int $userId): int
    {
        return $this->update($userId, ['last_login' => date('Y-m-d H:i:s')]);
    }

    /**
     * البحث في المستخدمين
     */
    public function searchUsers(string $term, int $limit = 20): array
    {
        $query = "
            SELECT u.*, r.role_name 
            FROM {$this->table} u 
            LEFT JOIN roles r ON u.role_id = r.role_id 
            WHERE (u.name LIKE :term OR u.username LIKE :term OR u.email LIKE :term)
            ORDER BY u.name 
            LIMIT {$limit}
        ";
        
        return $this->db->select($query, ['term' => "%{$term}%"]);
    }

    /**
     * الحصول على المستخدمين حسب الدور
     */
    public function getUsersByRole(int $roleId): array
    {
        $query = "
            SELECT u.*, r.role_name 
            FROM {$this->table} u 
            LEFT JOIN roles r ON u.role_id = r.role_id 
            WHERE u.role_id = :role_id AND u.status = 'active'
            ORDER BY u.name
        ";
        
        return $this->db->select($query, ['role_id' => $roleId]);
    }

    /**
     * تفعيل/إلغاء تفعيل المستخدم
     */
    public function toggleStatus(int $userId): int
    {
        $user = $this->find($userId);
        if (!$user) {
            throw new \Exception('المستخدم غير موجود');
        }

        $newStatus = $user['status'] === 'active' ? 'inactive' : 'active';
        
        return $this->update($userId, ['status' => $newStatus]);
    }

    /**
     * التحقق من وجود اسم مستخدم
     */
    public function usernameExists(string $username, int $excludeUserId = null): bool
    {
        $where = 'username = :username';
        $params = ['username' => $username];
        
        if ($excludeUserId) {
            $where .= ' AND user_id != :exclude_id';
            $params['exclude_id'] = $excludeUserId;
        }
        
        return $this->db->exists($this->table, $where, $params);
    }

    /**
     * التحقق من وجود بريد إلكتروني
     */
    public function emailExists(string $email, int $excludeUserId = null): bool
    {
        $where = 'email = :email';
        $params = ['email' => $email];
        
        if ($excludeUserId) {
            $where .= ' AND user_id != :exclude_id';
            $params['exclude_id'] = $excludeUserId;
        }
        
        return $this->db->exists($this->table, $where, $params);
    }

    /**
     * إحصائيات المستخدمين
     */
    public function getStats(): array
    {
        $totalQuery = "SELECT COUNT(*) as total FROM {$this->table}";
        $activeQuery = "SELECT COUNT(*) as active FROM {$this->table} WHERE status = 'active'";
        $inactiveQuery = "SELECT COUNT(*) as inactive FROM {$this->table} WHERE status = 'inactive'";
        
        $total = $this->db->selectOne($totalQuery)['total'];
        $active = $this->db->selectOne($activeQuery)['active'];
        $inactive = $this->db->selectOne($inactiveQuery)['inactive'];
        
        return [
            'total' => $total,
            'active' => $active,
            'inactive' => $inactive
        ];
    }
}

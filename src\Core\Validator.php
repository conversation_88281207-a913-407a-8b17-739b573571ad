<?php

namespace LYstore\Core;

/**
 * فئة التحقق من صحة البيانات
 */
class Validator
{
    private $errors = [];
    private $data = [];

    /**
     * التحقق من صحة البيانات
     */
    public function validate(array $data, array $rules): array
    {
        $this->data = $data;
        $this->errors = [];

        foreach ($rules as $field => $ruleSet) {
            $this->validateField($field, $ruleSet);
        }

        return [
            'valid' => empty($this->errors),
            'errors' => $this->errors
        ];
    }

    /**
     * التحقق من صحة حقل واحد
     */
    private function validateField(string $field, string $ruleSet): void
    {
        $rules = explode('|', $ruleSet);
        $value = $this->data[$field] ?? null;

        foreach ($rules as $rule) {
            $this->applyRule($field, $value, $rule);
        }
    }

    /**
     * تطبيق قاعدة واحدة
     */
    private function applyRule(string $field, $value, string $rule): void
    {
        if (strpos($rule, ':') !== false) {
            [$ruleName, $parameter] = explode(':', $rule, 2);
        } else {
            $ruleName = $rule;
            $parameter = null;
        }

        switch ($ruleName) {
            case 'required':
                $this->validateRequired($field, $value);
                break;
            case 'email':
                $this->validateEmail($field, $value);
                break;
            case 'min':
                $this->validateMin($field, $value, (int) $parameter);
                break;
            case 'max':
                $this->validateMax($field, $value, (int) $parameter);
                break;
            case 'numeric':
                $this->validateNumeric($field, $value);
                break;
            case 'integer':
                $this->validateInteger($field, $value);
                break;
            case 'decimal':
                $this->validateDecimal($field, $value, $parameter);
                break;
            case 'unique':
                $this->validateUnique($field, $value, $parameter);
                break;
            case 'exists':
                $this->validateExists($field, $value, $parameter);
                break;
            case 'phone':
                $this->validatePhone($field, $value);
                break;
            case 'date':
                $this->validateDate($field, $value);
                break;
            case 'in':
                $this->validateIn($field, $value, $parameter);
                break;
            case 'regex':
                $this->validateRegex($field, $value, $parameter);
                break;
        }
    }

    /**
     * التحقق من الحقول المطلوبة
     */
    private function validateRequired(string $field, $value): void
    {
        if (is_null($value) || (is_string($value) && trim($value) === '')) {
            $this->addError($field, 'هذا الحقل مطلوب');
        }
    }

    /**
     * التحقق من البريد الإلكتروني
     */
    private function validateEmail(string $field, $value): void
    {
        if (!is_null($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
            $this->addError($field, 'يجب أن يكون بريد إلكتروني صحيح');
        }
    }

    /**
     * التحقق من الحد الأدنى للطول
     */
    private function validateMin(string $field, $value, int $min): void
    {
        if (!is_null($value) && strlen($value) < $min) {
            $this->addError($field, "يجب أن يكون على الأقل {$min} أحرف");
        }
    }

    /**
     * التحقق من الحد الأقصى للطول
     */
    private function validateMax(string $field, $value, int $max): void
    {
        if (!is_null($value) && strlen($value) > $max) {
            $this->addError($field, "يجب أن يكون أقل من {$max} حرف");
        }
    }

    /**
     * التحقق من كون القيمة رقمية
     */
    private function validateNumeric(string $field, $value): void
    {
        if (!is_null($value) && !is_numeric($value)) {
            $this->addError($field, 'يجب أن يكون رقم');
        }
    }

    /**
     * التحقق من كون القيمة عدد صحيح
     */
    private function validateInteger(string $field, $value): void
    {
        if (!is_null($value) && !filter_var($value, FILTER_VALIDATE_INT)) {
            $this->addError($field, 'يجب أن يكون عدد صحيح');
        }
    }

    /**
     * التحقق من كون القيمة رقم عشري
     */
    private function validateDecimal(string $field, $value, ?string $precision): void
    {
        if (is_null($value)) return;

        if (!is_numeric($value)) {
            $this->addError($field, 'يجب أن يكون رقم عشري');
            return;
        }

        if ($precision) {
            [$integer, $decimal] = explode(',', $precision);
            $pattern = "/^\d{1,{$integer}}(\.\d{1,{$decimal}})?$/";
            
            if (!preg_match($pattern, $value)) {
                $this->addError($field, "يجب أن يكون رقم عشري بحد أقصى {$integer} أرقام و {$decimal} خانات عشرية");
            }
        }
    }

    /**
     * التحقق من عدم تكرار القيمة في قاعدة البيانات
     */
    private function validateUnique(string $field, $value, string $table): void
    {
        if (is_null($value)) return;

        $db = Database::getInstance();
        $exists = $db->exists($table, "{$field} = :value", ['value' => $value]);
        
        if ($exists) {
            $this->addError($field, 'هذه القيمة مستخدمة مسبقاً');
        }
    }

    /**
     * التحقق من وجود القيمة في قاعدة البيانات
     */
    private function validateExists(string $field, $value, string $table): void
    {
        if (is_null($value)) return;

        $db = Database::getInstance();
        $exists = $db->exists($table, "{$field} = :value", ['value' => $value]);
        
        if (!$exists) {
            $this->addError($field, 'هذه القيمة غير موجودة');
        }
    }

    /**
     * التحقق من رقم الهاتف
     */
    private function validatePhone(string $field, $value): void
    {
        if (is_null($value)) return;

        // نمط رقم الهاتف الليبي
        $pattern = '/^(091|092|093|094|095)\d{7}$/';
        
        if (!preg_match($pattern, $value)) {
            $this->addError($field, 'رقم الهاتف غير صحيح (يجب أن يبدأ بـ 091, 092, 093, 094, أو 095)');
        }
    }

    /**
     * التحقق من التاريخ
     */
    private function validateDate(string $field, $value): void
    {
        if (is_null($value)) return;

        $date = \DateTime::createFromFormat('Y-m-d', $value);
        
        if (!$date || $date->format('Y-m-d') !== $value) {
            $this->addError($field, 'تاريخ غير صحيح (يجب أن يكون بصيغة YYYY-MM-DD)');
        }
    }

    /**
     * التحقق من كون القيمة ضمن قائمة محددة
     */
    private function validateIn(string $field, $value, string $list): void
    {
        if (is_null($value)) return;

        $allowedValues = explode(',', $list);
        
        if (!in_array($value, $allowedValues)) {
            $this->addError($field, 'القيمة غير مسموحة');
        }
    }

    /**
     * التحقق باستخدام تعبير نمطي
     */
    private function validateRegex(string $field, $value, string $pattern): void
    {
        if (is_null($value)) return;

        if (!preg_match($pattern, $value)) {
            $this->addError($field, 'تنسيق غير صحيح');
        }
    }

    /**
     * إضافة خطأ
     */
    private function addError(string $field, string $message): void
    {
        if (!isset($this->errors[$field])) {
            $this->errors[$field] = [];
        }
        
        $this->errors[$field][] = $message;
    }

    /**
     * الحصول على الأخطاء
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * التحقق من وجود أخطاء
     */
    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }

    /**
     * الحصول على أول خطأ لحقل معين
     */
    public function getFirstError(string $field): ?string
    {
        return $this->errors[$field][0] ?? null;
    }
}

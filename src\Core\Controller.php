<?php

namespace LYstore\Core;

/**
 * المتحكم الأساسي لجميع المتحكمات في النظام
 */
abstract class Controller
{
    protected $request;
    protected $response;
    protected $session;

    public function __construct()
    {
        $this->request = new Request();
        $this->response = new Response();
        $this->session = new Session();
    }

    /**
     * عرض صفحة
     */
    protected function view(string $view, array $data = []): void
    {
        $viewPath = __DIR__ . '/../../views/' . str_replace('.', '/', $view) . '.php';
        
        if (!file_exists($viewPath)) {
            throw new \Exception("العرض غير موجود: {$view}");
        }
        
        // استخراج المتغيرات للعرض
        extract($data);
        
        // بدء التخزين المؤقت للإخراج
        ob_start();
        
        // تضمين ملف العرض
        include $viewPath;
        
        // الحصول على المحتوى وإنهاء التخزين المؤقت
        $content = ob_get_clean();
        
        // إرسال المحتوى
        echo $content;
    }

    /**
     * إرجاع استجابة JSON
     */
    protected function json(array $data, int $statusCode = 200): void
    {
        $this->response->json($data, $statusCode);
    }

    /**
     * إعادة توجيه
     */
    protected function redirect(string $url): void
    {
        $this->response->redirect($url);
    }

    /**
     * إعادة توجيه مع رسالة
     */
    protected function redirectWithMessage(string $url, string $message, string $type = 'success'): void
    {
        $this->session->setFlash($type, $message);
        $this->redirect($url);
    }

    /**
     * التحقق من صحة البيانات
     */
    protected function validate(array $data, array $rules): array
    {
        $validator = new Validator();
        return $validator->validate($data, $rules);
    }

    /**
     * التحقق من تسجيل الدخول
     */
    protected function requireAuth(): void
    {
        if (!$this->session->get('user_id')) {
            $this->redirect('/login');
            exit;
        }
    }

    /**
     * التحقق من الصلاحيات
     */
    protected function requirePermission(string $permission): void
    {
        $this->requireAuth();
        
        $userRole = $this->session->get('user_role');
        
        // مدير النظام له جميع الصلاحيات
        if ($userRole === 'مدير النظام') {
            return;
        }
        
        // التحقق من الصلاحيات المحددة
        $permissions = $this->getUserPermissions($userRole);
        
        if (!in_array($permission, $permissions)) {
            $this->json(['error' => 'ليس لديك صلاحية للوصول لهذه الصفحة'], 403);
            exit;
        }
    }

    /**
     * الحصول على صلاحيات المستخدم
     */
    private function getUserPermissions(string $role): array
    {
        $permissions = [
            'مدير مبيعات' => [
                'sales.view', 'sales.create', 'sales.edit', 'sales.delete',
                'customers.view', 'customers.create', 'customers.edit',
                'products.view', 'quotations.view', 'quotations.create'
            ],
            'موظف مبيعات' => [
                'sales.view', 'sales.create', 'customers.view', 'products.view',
                'quotations.view', 'quotations.create'
            ],
            'مدير مشتريات' => [
                'purchases.view', 'purchases.create', 'purchases.edit', 'purchases.delete',
                'suppliers.view', 'suppliers.create', 'suppliers.edit',
                'products.view'
            ],
            'موظف مشتريات' => [
                'purchases.view', 'purchases.create', 'suppliers.view', 'products.view'
            ],
            'أمين مخزن' => [
                'inventory.view', 'inventory.create', 'inventory.edit',
                'products.view', 'warehouses.view', 'stock_movements.view'
            ],
            'محاسب' => [
                'invoices.view', 'payments.view', 'reports.view',
                'customers.view', 'suppliers.view'
            ],
            'مدقق' => [
                'sales.view', 'purchases.view', 'inventory.view',
                'invoices.view', 'payments.view', 'reports.view'
            ]
        ];
        
        return $permissions[$role] ?? [];
    }

    /**
     * تسجيل العمليات في سجل النشاط
     */
    protected function logActivity(string $action, string $table, int $recordId, array $oldValues = [], array $newValues = []): void
    {
        $auditLog = new \LYstore\Models\AuditLog();
        
        $auditLog->create([
            'user_id' => $this->session->get('user_id'),
            'action' => $action,
            'table_name' => $table,
            'record_id' => $recordId,
            'old_values' => !empty($oldValues) ? json_encode($oldValues, JSON_UNESCAPED_UNICODE) : null,
            'new_values' => !empty($newValues) ? json_encode($newValues, JSON_UNESCAPED_UNICODE) : null,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    }

    /**
     * معالجة الأخطاء
     */
    protected function handleError(\Exception $e, string $message = 'حدث خطأ غير متوقع'): void
    {
        // تسجيل الخطأ
        error_log($e->getMessage());
        
        // إرسال استجابة خطأ
        if ($this->request->isAjax()) {
            $this->json(['error' => $message], 500);
        } else {
            $this->view('errors.500', ['message' => $message]);
        }
    }

    /**
     * تنسيق التاريخ للعرض
     */
    protected function formatDate(string $date, string $format = 'Y-m-d H:i'): string
    {
        return date($format, strtotime($date));
    }

    /**
     * تنسيق المبلغ للعرض
     */
    protected function formatCurrency(float $amount, string $currency = 'د.ل'): string
    {
        return number_format($amount, 2) . ' ' . $currency;
    }

    /**
     * إنشاء رقم مرجعي فريد
     */
    protected function generateReferenceNumber(string $prefix): string
    {
        $date = date('Ymd');
        $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        return $prefix . $date . $random;
    }

    /**
     * رفع ملف
     */
    protected function uploadFile(array $file, string $directory = 'uploads'): ?string
    {
        if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
            return null;
        }
        
        $uploadDir = __DIR__ . '/../../public/' . $directory . '/';
        
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $fileName = uniqid() . '_' . $file['name'];
        $filePath = $uploadDir . $fileName;
        
        if (move_uploaded_file($file['tmp_name'], $filePath)) {
            return $directory . '/' . $fileName;
        }
        
        return null;
    }
}

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام LYstoreE</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            margin: 20px;
        }
        
        .login-form {
            padding: 60px 40px;
        }
        
        .login-image {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 40px;
        }
        
        .login-image h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .login-image p {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .login-image i {
            font-size: 4rem;
            margin-bottom: 30px;
            opacity: 0.8;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-login:disabled {
            opacity: 0.7;
            transform: none;
            box-shadow: none;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }
        
        .alert {
            border: none;
            border-radius: 10px;
            padding: 15px 20px;
        }
        
        .input-group-text {
            background: transparent;
            border: 2px solid #e9ecef;
            border-left: none;
            border-radius: 0 10px 10px 0;
        }
        
        .input-group .form-control {
            border-left: none;
            border-radius: 10px 0 0 10px;
        }
        
        .input-group .form-control:focus + .input-group-text {
            border-color: #667eea;
        }
        
        .loading-spinner {
            display: none;
        }
        
        .loading-spinner.show {
            display: inline-block;
        }
        
        @media (max-width: 768px) {
            .login-image {
                display: none;
            }
            
            .login-form {
                padding: 40px 30px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="row g-0">
            <!-- صورة/معلومات النظام -->
            <div class="col-lg-6 login-image">
                <div>
                    <i class="fas fa-store"></i>
                    <h2>نظام LYstoreE</h2>
                    <p>
                        نظام متكامل لإدارة المبيعات والمشتريات والمخازن
                        <br>
                        مصمم خصيصاً للشركات الليبية
                    </p>
                    <div class="mt-4">
                        <small>
                            <i class="fas fa-shield-alt me-2"></i>
                            آمن وموثوق
                        </small>
                        <br>
                        <small>
                            <i class="fas fa-clock me-2"></i>
                            متاح 24/7
                        </small>
                        <br>
                        <small>
                            <i class="fas fa-headset me-2"></i>
                            دعم فني متخصص
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- نموذج تسجيل الدخول -->
            <div class="col-lg-6 login-form">
                <div class="text-center mb-4">
                    <h3 class="fw-bold text-dark">تسجيل الدخول</h3>
                    <p class="text-muted">أدخل بياناتك للوصول إلى النظام</p>
                </div>
                
                <!-- رسائل التنبيه -->
                <div id="alertContainer"></div>
                
                <!-- نموذج تسجيل الدخول -->
                <form id="loginForm">
                    <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                    
                    <div class="mb-3">
                        <label for="username" class="form-label">
                            <i class="fas fa-user me-2"></i>
                            اسم المستخدم
                        </label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="username" name="username" 
                                   placeholder="أدخل اسم المستخدم" required>
                            <span class="input-group-text">
                                <i class="fas fa-user text-muted"></i>
                            </span>
                        </div>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock me-2"></i>
                            كلمة المرور
                        </label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="password" name="password" 
                                   placeholder="أدخل كلمة المرور" required>
                            <span class="input-group-text">
                                <i class="fas fa-lock text-muted"></i>
                            </span>
                        </div>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember" name="remember">
                        <label class="form-check-label" for="remember">
                            تذكرني
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-login" id="loginBtn">
                        <span class="btn-text">تسجيل الدخول</span>
                        <span class="loading-spinner spinner-border spinner-border-sm me-2" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </span>
                    </button>
                </form>
                
                <div class="text-center mt-4">
                    <small class="text-muted">
                        نسيت كلمة المرور؟ 
                        <a href="#" class="text-decoration-none">اضغط هنا</a>
                    </small>
                </div>
                
                <hr class="my-4">
                
                <div class="text-center">
                    <small class="text-muted">
                        © 2025 نظام LYstoreE. جميع الحقوق محفوظة.
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <script>
        $(document).ready(function() {
            $('#loginForm').on('submit', function(e) {
                e.preventDefault();
                
                // إزالة رسائل الخطأ السابقة
                clearErrors();
                
                // تعطيل الزر وإظهار التحميل
                const loginBtn = $('#loginBtn');
                const btnText = loginBtn.find('.btn-text');
                const spinner = loginBtn.find('.loading-spinner');
                
                loginBtn.prop('disabled', true);
                btnText.text('جاري تسجيل الدخول...');
                spinner.addClass('show');
                
                // إرسال البيانات
                $.ajax({
                    url: '/login',
                    method: 'POST',
                    data: $(this).serialize(),
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            showAlert('success', response.message);
                            
                            // إعادة توجيه بعد ثانيتين
                            setTimeout(function() {
                                window.location.href = response.redirect || '/dashboard';
                            }, 1000);
                        } else {
                            showAlert('danger', response.message);
                            resetButton();
                        }
                    },
                    error: function(xhr) {
                        const response = xhr.responseJSON;
                        
                        if (response && response.errors) {
                            showValidationErrors(response.errors);
                        } else {
                            showAlert('danger', response?.message || 'حدث خطأ غير متوقع');
                        }
                        
                        resetButton();
                    }
                });
            });
            
            function resetButton() {
                const loginBtn = $('#loginBtn');
                const btnText = loginBtn.find('.btn-text');
                const spinner = loginBtn.find('.loading-spinner');
                
                loginBtn.prop('disabled', false);
                btnText.text('تسجيل الدخول');
                spinner.removeClass('show');
            }
            
            function showAlert(type, message) {
                const alertHtml = `
                    <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `;
                $('#alertContainer').html(alertHtml);
            }
            
            function showValidationErrors(errors) {
                for (const field in errors) {
                    const input = $(`[name="${field}"]`);
                    const feedback = input.siblings('.invalid-feedback');
                    
                    input.addClass('is-invalid');
                    feedback.text(errors[field][0]);
                }
            }
            
            function clearErrors() {
                $('.form-control').removeClass('is-invalid');
                $('.invalid-feedback').text('');
                $('#alertContainer').empty();
            }
            
            // إزالة رسائل الخطأ عند الكتابة
            $('.form-control').on('input', function() {
                $(this).removeClass('is-invalid');
                $(this).siblings('.invalid-feedback').text('');
            });
        });
    </script>
</body>
</html>

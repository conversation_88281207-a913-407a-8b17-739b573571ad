/**
 * نظام LYstoreE - ملف JavaScript الرئيسي
 */

// إعدادات عامة
const LYstore = {
    config: {
        currency: 'د.ل',
        dateFormat: 'YYYY-MM-DD',
        timeFormat: 'HH:mm',
        language: 'ar'
    },
    
    // وظائف مساعدة
    utils: {
        // تنسيق الأرقام
        formatNumber: function(number, decimals = 2) {
            return new Intl.NumberFormat('ar-LY', {
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals
            }).format(number);
        },
        
        // تنسيق العملة
        formatCurrency: function(amount, currency = 'د.ل') {
            return this.formatNumber(amount, 2) + ' ' + currency;
        },
        
        // تنسيق التاريخ
        formatDate: function(date, format = 'YYYY-MM-DD') {
            const d = new Date(date);
            const year = d.getFullYear();
            const month = String(d.getMonth() + 1).padStart(2, '0');
            const day = String(d.getDate()).padStart(2, '0');
            
            switch(format) {
                case 'DD/MM/YYYY':
                    return `${day}/${month}/${year}`;
                case 'DD-MM-YYYY':
                    return `${day}-${month}-${year}`;
                default:
                    return `${year}-${month}-${day}`;
            }
        },
        
        // تنسيق الوقت
        formatTime: function(time) {
            const t = new Date(time);
            const hours = String(t.getHours()).padStart(2, '0');
            const minutes = String(t.getMinutes()).padStart(2, '0');
            return `${hours}:${minutes}`;
        },
        
        // تنسيق التاريخ والوقت
        formatDateTime: function(datetime) {
            return this.formatDate(datetime) + ' ' + this.formatTime(datetime);
        },
        
        // تحويل النص إلى slug
        slugify: function(text) {
            return text.toString().toLowerCase()
                .replace(/\s+/g, '-')
                .replace(/[^\w\-]+/g, '')
                .replace(/\-\-+/g, '-')
                .replace(/^-+/, '')
                .replace(/-+$/, '');
        },
        
        // إنشاء UUID بسيط
        generateUUID: function() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }
    },
    
    // وظائف واجهة المستخدم
    ui: {
        // إظهار مؤشر التحميل
        showLoading: function(message = 'جاري التحميل...') {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.querySelector('p').textContent = message;
                overlay.classList.add('show');
            }
        },
        
        // إخفاء مؤشر التحميل
        hideLoading: function() {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.classList.remove('show');
            }
        },
        
        // إظهار تنبيه
        showAlert: function(type, message, title = '') {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${title ? '<strong>' + title + '</strong><br>' : ''}
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            // البحث عن حاوي التنبيهات
            let container = document.getElementById('alertContainer');
            if (!container) {
                container = document.createElement('div');
                container.id = 'alertContainer';
                container.className = 'alert-container';
                document.querySelector('.main-content').prepend(container);
            }
            
            container.insertAdjacentHTML('beforeend', alertHtml);
            
            // إزالة التنبيه تلقائياً بعد 5 ثوان
            setTimeout(() => {
                const alerts = container.querySelectorAll('.alert');
                if (alerts.length > 0) {
                    alerts[0].remove();
                }
            }, 5000);
        },
        
        // تأكيد الحذف
        confirmDelete: function(message = 'هل أنت متأكد من الحذف؟') {
            return confirm(message);
        },
        
        // إظهار مودال
        showModal: function(modalId) {
            const modal = new bootstrap.Modal(document.getElementById(modalId));
            modal.show();
        },
        
        // إخفاء مودال
        hideModal: function(modalId) {
            const modal = bootstrap.Modal.getInstance(document.getElementById(modalId));
            if (modal) {
                modal.hide();
            }
        }
    },
    
    // وظائف AJAX
    ajax: {
        // إعدادات افتراضية
        defaults: {
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        },
        
        // طلب GET
        get: function(url, options = {}) {
            return this.request(url, { ...options, method: 'GET' });
        },
        
        // طلب POST
        post: function(url, data = {}, options = {}) {
            return this.request(url, { 
                ...options, 
                method: 'POST', 
                body: JSON.stringify(data) 
            });
        },
        
        // طلب PUT
        put: function(url, data = {}, options = {}) {
            return this.request(url, { 
                ...options, 
                method: 'PUT', 
                body: JSON.stringify(data) 
            });
        },
        
        // طلب DELETE
        delete: function(url, options = {}) {
            return this.request(url, { ...options, method: 'DELETE' });
        },
        
        // طلب عام
        request: function(url, options = {}) {
            const config = {
                ...this.defaults,
                ...options,
                headers: {
                    ...this.defaults.headers,
                    ...options.headers
                }
            };
            
            return fetch(url, config)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .catch(error => {
                    console.error('AJAX Error:', error);
                    throw error;
                });
        },
        
        // إرسال نموذج
        submitForm: function(formElement, options = {}) {
            const formData = new FormData(formElement);
            const data = Object.fromEntries(formData.entries());
            
            const url = options.url || formElement.action || window.location.pathname;
            const method = options.method || formElement.method || 'POST';
            
            return this.request(url, {
                method: method,
                body: JSON.stringify(data),
                ...options
            });
        }
    },
    
    // وظائف التحقق من صحة البيانات
    validation: {
        // قواعد التحقق
        rules: {
            required: function(value) {
                return value !== null && value !== undefined && value.toString().trim() !== '';
            },
            
            email: function(value) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(value);
            },
            
            phone: function(value) {
                const phoneRegex = /^(091|092|093|094|095)\d{7}$/;
                return phoneRegex.test(value);
            },
            
            numeric: function(value) {
                return !isNaN(value) && !isNaN(parseFloat(value));
            },
            
            min: function(value, min) {
                return value.toString().length >= min;
            },
            
            max: function(value, max) {
                return value.toString().length <= max;
            }
        },
        
        // التحقق من حقل واحد
        validateField: function(value, rules) {
            const errors = [];
            
            for (const rule of rules) {
                const [ruleName, ...params] = rule.split(':');
                
                if (this.rules[ruleName]) {
                    if (!this.rules[ruleName](value, ...params)) {
                        errors.push(this.getErrorMessage(ruleName, params));
                    }
                }
            }
            
            return errors;
        },
        
        // التحقق من نموذج كامل
        validateForm: function(formElement) {
            const errors = {};
            const inputs = formElement.querySelectorAll('[data-validate]');
            
            inputs.forEach(input => {
                const rules = input.dataset.validate.split('|');
                const fieldErrors = this.validateField(input.value, rules);
                
                if (fieldErrors.length > 0) {
                    errors[input.name] = fieldErrors;
                }
            });
            
            return {
                valid: Object.keys(errors).length === 0,
                errors: errors
            };
        },
        
        // رسائل الخطأ
        getErrorMessage: function(rule, params) {
            const messages = {
                required: 'هذا الحقل مطلوب',
                email: 'يجب أن يكون بريد إلكتروني صحيح',
                phone: 'رقم الهاتف غير صحيح',
                numeric: 'يجب أن يكون رقم',
                min: `يجب أن يكون على الأقل ${params[0]} أحرف`,
                max: `يجب أن يكون أقل من ${params[0]} حرف`
            };
            
            return messages[rule] || 'قيمة غير صحيحة';
        }
    },
    
    // وظائف التخزين المحلي
    storage: {
        // حفظ بيانات
        set: function(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (e) {
                console.error('Storage error:', e);
                return false;
            }
        },
        
        // استرجاع بيانات
        get: function(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (e) {
                console.error('Storage error:', e);
                return defaultValue;
            }
        },
        
        // حذف بيانات
        remove: function(key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (e) {
                console.error('Storage error:', e);
                return false;
            }
        },
        
        // مسح جميع البيانات
        clear: function() {
            try {
                localStorage.clear();
                return true;
            } catch (e) {
                console.error('Storage error:', e);
                return false;
            }
        }
    }
};

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تفعيل popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // تفعيل التحقق من النماذج
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
    
    // تفعيل البحث التلقائي
    const searchInputs = document.querySelectorAll('[data-search]');
    searchInputs.forEach(function(input) {
        let timeout;
        input.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                const searchUrl = input.dataset.search;
                const query = input.value.trim();
                
                if (query.length >= 2) {
                    LYstore.ajax.get(`${searchUrl}?q=${encodeURIComponent(query)}`)
                        .then(data => {
                            // معالجة نتائج البحث
                            console.log('Search results:', data);
                        })
                        .catch(error => {
                            console.error('Search error:', error);
                        });
                }
            }, 300);
        });
    });
    
    // تفعيل الحفظ التلقائي للنماذج
    const autoSaveForms = document.querySelectorAll('[data-autosave]');
    autoSaveForms.forEach(function(form) {
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(function(input) {
            input.addEventListener('change', function() {
                const formData = new FormData(form);
                const data = Object.fromEntries(formData.entries());
                LYstore.storage.set(`autosave_${form.id}`, data);
            });
        });
        
        // استرجاع البيانات المحفوظة
        const savedData = LYstore.storage.get(`autosave_${form.id}`);
        if (savedData) {
            Object.keys(savedData).forEach(key => {
                const input = form.querySelector(`[name="${key}"]`);
                if (input) {
                    input.value = savedData[key];
                }
            });
        }
    });
});

// تصدير الكائن للاستخدام العام
window.LYstore = LYstore;

<?php
/**
 * مسارات الويب لنظام LYstoreE
 */

// الصفحة الرئيسية
$router->get('/', 'HomeController@index');
$router->get('/dashboard', 'DashboardController@index', ['Auth']);

// مسارات المصادقة
$router->get('/login', 'AuthController@showLogin');
$router->post('/login', 'AuthController@login');
$router->post('/logout', 'AuthController@logout');

// مسارات إدارة المستخدمين
$router->group('/users', function($router) {
    $router->get('/', 'UserController@index');
    $router->get('/create', 'UserController@create');
    $router->post('/', 'UserController@store');
    $router->get('/{id}', 'UserController@show');
    $router->get('/{id}/edit', 'UserController@edit');
    $router->put('/{id}', 'UserController@update');
    $router->delete('/{id}', 'UserController@delete');
}, ['Auth', 'AdminOnly']);

// مسارات إدارة العملاء
$router->group('/customers', function($router) {
    $router->get('/', 'CustomerController@index');
    $router->get('/create', 'CustomerController@create');
    $router->post('/', 'CustomerController@store');
    $router->get('/{id}', 'CustomerController@show');
    $router->get('/{id}/edit', 'CustomerController@edit');
    $router->put('/{id}', 'CustomerController@update');
    $router->delete('/{id}', 'CustomerController@delete');
    $router->get('/search', 'CustomerController@search');
}, ['Auth']);

// مسارات إدارة الموردين
$router->group('/suppliers', function($router) {
    $router->get('/', 'SupplierController@index');
    $router->get('/create', 'SupplierController@create');
    $router->post('/', 'SupplierController@store');
    $router->get('/{id}', 'SupplierController@show');
    $router->get('/{id}/edit', 'SupplierController@edit');
    $router->put('/{id}', 'SupplierController@update');
    $router->delete('/{id}', 'SupplierController@delete');
}, ['Auth']);

// مسارات إدارة المنتجات
$router->group('/products', function($router) {
    $router->get('/', 'ProductController@index');
    $router->get('/create', 'ProductController@create');
    $router->post('/', 'ProductController@store');
    $router->get('/{id}', 'ProductController@show');
    $router->get('/{id}/edit', 'ProductController@edit');
    $router->put('/{id}', 'ProductController@update');
    $router->delete('/{id}', 'ProductController@delete');
    $router->get('/search', 'ProductController@search');
    $router->get('/barcode/{barcode}', 'ProductController@findByBarcode');
}, ['Auth']);

// مسارات إدارة التصنيفات
$router->group('/categories', function($router) {
    $router->get('/', 'CategoryController@index');
    $router->post('/', 'CategoryController@store');
    $router->put('/{id}', 'CategoryController@update');
    $router->delete('/{id}', 'CategoryController@delete');
}, ['Auth']);

// مسارات أوامر البيع
$router->group('/sales-orders', function($router) {
    $router->get('/', 'SalesOrderController@index');
    $router->get('/create', 'SalesOrderController@create');
    $router->post('/', 'SalesOrderController@store');
    $router->get('/{id}', 'SalesOrderController@show');
    $router->get('/{id}/edit', 'SalesOrderController@edit');
    $router->put('/{id}', 'SalesOrderController@update');
    $router->delete('/{id}', 'SalesOrderController@delete');
    $router->post('/{id}/convert-to-invoice', 'SalesOrderController@convertToInvoice');
}, ['Auth']);

// مسارات أوامر الشراء
$router->group('/purchase-orders', function($router) {
    $router->get('/', 'PurchaseOrderController@index');
    $router->get('/create', 'PurchaseOrderController@create');
    $router->post('/', 'PurchaseOrderController@store');
    $router->get('/{id}', 'PurchaseOrderController@show');
    $router->get('/{id}/edit', 'PurchaseOrderController@edit');
    $router->put('/{id}', 'PurchaseOrderController@update');
    $router->delete('/{id}', 'PurchaseOrderController@delete');
    $router->post('/{id}/receive', 'PurchaseOrderController@receive');
}, ['Auth']);

// مسارات عروض الأسعار
$router->group('/quotations', function($router) {
    $router->get('/', 'QuotationController@index');
    $router->get('/create', 'QuotationController@create');
    $router->post('/', 'QuotationController@store');
    $router->get('/{id}', 'QuotationController@show');
    $router->get('/{id}/edit', 'QuotationController@edit');
    $router->put('/{id}', 'QuotationController@update');
    $router->delete('/{id}', 'QuotationController@delete');
    $router->post('/{id}/convert-to-order', 'QuotationController@convertToOrder');
    $router->get('/{id}/pdf', 'QuotationController@generatePdf');
}, ['Auth']);

// مسارات الفواتير
$router->group('/invoices', function($router) {
    $router->get('/', 'InvoiceController@index');
    $router->get('/create', 'InvoiceController@create');
    $router->post('/', 'InvoiceController@store');
    $router->get('/{id}', 'InvoiceController@show');
    $router->get('/{id}/edit', 'InvoiceController@edit');
    $router->put('/{id}', 'InvoiceController@update');
    $router->delete('/{id}', 'InvoiceController@delete');
    $router->get('/{id}/pdf', 'InvoiceController@generatePdf');
    $router->post('/{id}/payment', 'InvoiceController@addPayment');
}, ['Auth']);

// مسارات المدفوعات
$router->group('/payments', function($router) {
    $router->get('/', 'PaymentController@index');
    $router->get('/create', 'PaymentController@create');
    $router->post('/', 'PaymentController@store');
    $router->get('/{id}', 'PaymentController@show');
    $router->delete('/{id}', 'PaymentController@delete');
}, ['Auth']);

// مسارات إدارة المخازن
$router->group('/warehouses', function($router) {
    $router->get('/', 'WarehouseController@index');
    $router->post('/', 'WarehouseController@store');
    $router->put('/{id}', 'WarehouseController@update');
    $router->delete('/{id}', 'WarehouseController@delete');
    $router->get('/{id}/stock', 'WarehouseController@stock');
}, ['Auth']);

// مسارات حركة المخزون
$router->group('/stock-movements', function($router) {
    $router->get('/', 'StockMovementController@index');
    $router->post('/', 'StockMovementController@store');
    $router->get('/{id}', 'StockMovementController@show');
    $router->post('/transfer', 'StockMovementController@transfer');
    $router->post('/adjustment', 'StockMovementController@adjustment');
}, ['Auth']);

// مسارات التقارير
$router->group('/reports', function($router) {
    $router->get('/', 'ReportController@index');
    $router->get('/sales', 'ReportController@sales');
    $router->get('/purchases', 'ReportController@purchases');
    $router->get('/inventory', 'ReportController@inventory');
    $router->get('/financial', 'ReportController@financial');
    $router->get('/customers', 'ReportController@customers');
    $router->get('/suppliers', 'ReportController@suppliers');
    $router->post('/generate', 'ReportController@generate');
}, ['Auth']);

// مسارات API
$router->group('/api', function($router) {
    $router->get('/products/search', 'Api\\ProductController@search');
    $router->get('/customers/search', 'Api\\CustomerController@search');
    $router->get('/suppliers/search', 'Api\\SupplierController@search');
    $router->get('/stock/check', 'Api\\StockController@check');
    $router->get('/dashboard/stats', 'Api\\DashboardController@stats');
}, ['Auth']);

// مسارات الإعدادات
$router->group('/settings', function($router) {
    $router->get('/', 'SettingController@index');
    $router->post('/', 'SettingController@update');
    $router->get('/backup', 'SettingController@backup');
    $router->post('/restore', 'SettingController@restore');
}, ['Auth', 'AdminOnly']);

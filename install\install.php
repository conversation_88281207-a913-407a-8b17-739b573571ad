<?php
/**
 * ملف تثبيت نظام LYstoreE
 */

// تحديد مسار الجذر
define('ROOT_PATH', dirname(__DIR__));

// تحميل النظام الأساسي
require_once ROOT_PATH . '/src/Core/Autoloader.php';

use LYstore\Core\Autoloader;
use LYstore\Core\Database;

// تهيئة النظام
Autoloader::bootstrap();

/**
 * فئة التثبيت
 */
class Installer
{
    private $db;
    private $errors = [];
    private $success = [];

    public function __construct()
    {
        $this->checkRequirements();
    }

    /**
     * التحقق من المتطلبات
     */
    private function checkRequirements(): void
    {
        // التحقق من إصدار PHP
        if (version_compare(PHP_VERSION, '8.0.0', '<')) {
            $this->errors[] = 'يتطلب النظام PHP 8.0 أو أحدث. الإصدار الحالي: ' . PHP_VERSION;
        }

        // التحقق من الامتدادات المطلوبة
        $requiredExtensions = ['pdo', 'pdo_mysql', 'mbstring', 'json', 'openssl'];
        foreach ($requiredExtensions as $extension) {
            if (!extension_loaded($extension)) {
                $this->errors[] = "امتداد PHP مطلوب غير مثبت: {$extension}";
            }
        }

        // التحقق من صلاحيات الكتابة
        $writableDirs = [
            ROOT_PATH . '/storage',
            ROOT_PATH . '/storage/logs',
            ROOT_PATH . '/storage/sessions',
            ROOT_PATH . '/storage/backups',
            ROOT_PATH . '/public/uploads'
        ];

        foreach ($writableDirs as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
            
            if (!is_writable($dir)) {
                $this->errors[] = "المجلد غير قابل للكتابة: {$dir}";
            }
        }
    }

    /**
     * تثبيت قاعدة البيانات
     */
    public function installDatabase(): bool
    {
        try {
            // إنشاء قاعدة البيانات
            $this->createDatabase();
            
            // إنشاء الجداول
            $this->createTables();
            
            // إدراج البيانات الأساسية
            $this->insertSampleData();
            
            $this->success[] = 'تم تثبيت قاعدة البيانات بنجاح';
            return true;
            
        } catch (Exception $e) {
            $this->errors[] = 'خطأ في تثبيت قاعدة البيانات: ' . $e->getMessage();
            return false;
        }
    }

    /**
     * إنشاء قاعدة البيانات
     */
    private function createDatabase(): void
    {
        $config = require ROOT_PATH . '/config/database.php';
        $dbConfig = $config['connections']['mysql'];
        
        // الاتصال بدون تحديد قاعدة البيانات
        $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};charset={$dbConfig['charset']}";
        $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
        
        // إنشاء قاعدة البيانات
        $dbName = $dbConfig['database'];
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci");
        
        $this->success[] = "تم إنشاء قاعدة البيانات: {$dbName}";
    }

    /**
     * إنشاء الجداول
     */
    private function createTables(): void
    {
        $this->db = Database::getInstance();
        
        // تنفيذ ملف الجداول الأساسية
        $this->executeSqlFile(ROOT_PATH . '/database/schema.sql');
        
        // تنفيذ ملف الجداول الإضافية
        $this->executeSqlFile(ROOT_PATH . '/database/schema_part2.sql');
        
        $this->success[] = 'تم إنشاء جميع الجداول بنجاح';
    }

    /**
     * إدراج البيانات الأساسية
     */
    private function insertSampleData(): void
    {
        $this->executeSqlFile(ROOT_PATH . '/database/sample_data.sql');
        $this->success[] = 'تم إدراج البيانات الأساسية بنجاح';
    }

    /**
     * تنفيذ ملف SQL
     */
    private function executeSqlFile(string $filePath): void
    {
        if (!file_exists($filePath)) {
            throw new Exception("ملف SQL غير موجود: {$filePath}");
        }

        $sql = file_get_contents($filePath);
        
        // تقسيم الاستعلامات
        $statements = array_filter(
            array_map('trim', explode(';', $sql)),
            function($stmt) {
                return !empty($stmt) && !preg_match('/^--/', $stmt);
            }
        );

        foreach ($statements as $statement) {
            if (!empty($statement)) {
                $this->db->getConnection()->exec($statement);
            }
        }
    }

    /**
     * إنشاء ملف .env
     */
    public function createEnvFile(array $config): bool
    {
        try {
            $envContent = $this->generateEnvContent($config);
            file_put_contents(ROOT_PATH . '/.env', $envContent);
            
            $this->success[] = 'تم إنشاء ملف الإعدادات (.env) بنجاح';
            return true;
            
        } catch (Exception $e) {
            $this->errors[] = 'خطأ في إنشاء ملف الإعدادات: ' . $e->getMessage();
            return false;
        }
    }

    /**
     * إنشاء محتوى ملف .env
     */
    private function generateEnvContent(array $config): string
    {
        $content = "# إعدادات التطبيق\n";
        $content .= "APP_NAME=LYstoreE\n";
        $content .= "APP_ENV=production\n";
        $content .= "APP_DEBUG=false\n";
        $content .= "APP_URL={$config['app_url']}\n";
        $content .= "APP_KEY=" . base64_encode(random_bytes(32)) . "\n\n";
        
        $content .= "# إعدادات قاعدة البيانات\n";
        $content .= "DB_CONNECTION=mysql\n";
        $content .= "DB_HOST={$config['db_host']}\n";
        $content .= "DB_PORT={$config['db_port']}\n";
        $content .= "DB_DATABASE={$config['db_database']}\n";
        $content .= "DB_USERNAME={$config['db_username']}\n";
        $content .= "DB_PASSWORD={$config['db_password']}\n\n";
        
        $content .= "# إعدادات الشركة\n";
        $content .= "COMPANY_NAME=\"{$config['company_name']}\"\n";
        $content .= "COMPANY_ADDRESS=\"{$config['company_address']}\"\n";
        $content .= "COMPANY_PHONE=\"{$config['company_phone']}\"\n";
        $content .= "COMPANY_EMAIL=\"{$config['company_email']}\"\n\n";
        
        $content .= "# إعدادات النظام\n";
        $content .= "DEFAULT_CURRENCY=\"د.ل\"\n";
        $content .= "DEFAULT_TAX_RATE=14.00\n";
        $content .= "LOW_STOCK_ALERT=10\n";
        
        return $content;
    }

    /**
     * إنشاء مستخدم المدير
     */
    public function createAdminUser(array $userData): bool
    {
        try {
            $this->db = Database::getInstance();
            
            // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
            $existingUser = $this->db->selectOne(
                "SELECT user_id FROM users WHERE username = :username",
                ['username' => $userData['username']]
            );
            
            if ($existingUser) {
                $this->errors[] = 'اسم المستخدم موجود مسبقاً';
                return false;
            }
            
            // إنشاء المستخدم
            $userId = $this->db->insert('users', [
                'name' => $userData['name'],
                'username' => $userData['username'],
                'password_hash' => password_hash($userData['password'], PASSWORD_DEFAULT),
                'email' => $userData['email'],
                'phone' => $userData['phone'],
                'role_id' => 1, // مدير النظام
                'status' => 'active'
            ]);
            
            $this->success[] = 'تم إنشاء حساب المدير بنجاح';
            return true;
            
        } catch (Exception $e) {
            $this->errors[] = 'خطأ في إنشاء حساب المدير: ' . $e->getMessage();
            return false;
        }
    }

    /**
     * الحصول على الأخطاء
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * الحصول على رسائل النجاح
     */
    public function getSuccess(): array
    {
        return $this->success;
    }

    /**
     * التحقق من إمكانية التثبيت
     */
    public function canInstall(): bool
    {
        return empty($this->errors);
    }
}

// معالجة طلب التثبيت
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $installer = new Installer();
    
    if ($installer->canInstall()) {
        $config = [
            'app_url' => $_POST['app_url'] ?? 'http://localhost',
            'db_host' => $_POST['db_host'] ?? 'localhost',
            'db_port' => $_POST['db_port'] ?? '3306',
            'db_database' => $_POST['db_database'] ?? 'lystore',
            'db_username' => $_POST['db_username'] ?? 'root',
            'db_password' => $_POST['db_password'] ?? '',
            'company_name' => $_POST['company_name'] ?? 'شركة LYstore للتجارة العامة',
            'company_address' => $_POST['company_address'] ?? 'طرابلس، ليبيا',
            'company_phone' => $_POST['company_phone'] ?? '0911111111',
            'company_email' => $_POST['company_email'] ?? '<EMAIL>'
        ];
        
        $adminData = [
            'name' => $_POST['admin_name'] ?? 'مدير النظام',
            'username' => $_POST['admin_username'] ?? 'admin',
            'password' => $_POST['admin_password'] ?? '123456',
            'email' => $_POST['admin_email'] ?? '<EMAIL>',
            'phone' => $_POST['admin_phone'] ?? '0911111111'
        ];
        
        // تثبيت النظام
        $success = true;
        $success &= $installer->createEnvFile($config);
        $success &= $installer->installDatabase();
        $success &= $installer->createAdminUser($adminData);
        
        if ($success) {
            echo json_encode([
                'success' => true,
                'message' => 'تم تثبيت النظام بنجاح',
                'redirect' => '/login'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'errors' => $installer->getErrors()
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'errors' => $installer->getErrors()
        ]);
    }
    exit;
}

$installer = new Installer();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام LYstoreE</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .install-container { max-width: 800px; margin: 50px auto; }
        .step { display: none; }
        .step.active { display: block; }
        .error { color: #dc3545; }
        .success { color: #198754; }
    </style>
</head>
<body>
    <div class="container install-container">
        <div class="card">
            <div class="card-header text-center">
                <h2>تثبيت نظام LYstoreE</h2>
                <p class="text-muted">نظام إدارة المبيعات والمشتريات والمخازن</p>
            </div>
            <div class="card-body">
                <!-- خطوة التحقق من المتطلبات -->
                <div class="step active" id="step1">
                    <h4>التحقق من المتطلبات</h4>
                    
                    <?php if (!$installer->canInstall()): ?>
                        <div class="alert alert-danger">
                            <h5>يجب حل المشاكل التالية قبل المتابعة:</h5>
                            <ul>
                                <?php foreach ($installer->getErrors() as $error): ?>
                                    <li><?= htmlspecialchars($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        <button type="button" class="btn btn-primary" onclick="location.reload()">إعادة التحقق</button>
                    <?php else: ?>
                        <div class="alert alert-success">
                            <h5>✓ جميع المتطلبات متوفرة</h5>
                            <p>يمكنك المتابعة لتثبيت النظام</p>
                        </div>
                        <button type="button" class="btn btn-primary" onclick="nextStep()">التالي</button>
                    <?php endif; ?>
                </div>

                <!-- خطوة إعدادات قاعدة البيانات -->
                <div class="step" id="step2">
                    <h4>إعدادات قاعدة البيانات</h4>
                    <form id="installForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">خادم قاعدة البيانات</label>
                                    <input type="text" class="form-control" name="db_host" value="localhost" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المنفذ</label>
                                    <input type="text" class="form-control" name="db_port" value="3306" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم قاعدة البيانات</label>
                                    <input type="text" class="form-control" name="db_database" value="lystore" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المستخدم</label>
                                    <input type="text" class="form-control" name="db_username" value="root" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" name="db_password">
                        </div>
                        
                        <button type="button" class="btn btn-secondary" onclick="prevStep()">السابق</button>
                        <button type="button" class="btn btn-primary" onclick="nextStep()">التالي</button>
                    </form>
                </div>

                <!-- خطوة إعدادات الشركة -->
                <div class="step" id="step3">
                    <h4>إعدادات الشركة</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم الشركة</label>
                                <input type="text" class="form-control" name="company_name" value="شركة LYstore للتجارة العامة" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">عنوان الشركة</label>
                                <input type="text" class="form-control" name="company_address" value="طرابلس، ليبيا" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">هاتف الشركة</label>
                                <input type="text" class="form-control" name="company_phone" value="0911111111" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">بريد الشركة الإلكتروني</label>
                                <input type="email" class="form-control" name="company_email" value="<EMAIL>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">رابط الموقع</label>
                        <input type="url" class="form-control" name="app_url" value="http://localhost" required>
                    </div>
                    
                    <button type="button" class="btn btn-secondary" onclick="prevStep()">السابق</button>
                    <button type="button" class="btn btn-primary" onclick="nextStep()">التالي</button>
                </div>

                <!-- خطوة إنشاء حساب المدير -->
                <div class="step" id="step4">
                    <h4>إنشاء حساب المدير</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" name="admin_name" value="مدير النظام" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" name="admin_username" value="admin" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" name="admin_password" value="123456" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" name="admin_email" value="<EMAIL>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">رقم الهاتف</label>
                        <input type="text" class="form-control" name="admin_phone" value="0911111111" required>
                    </div>
                    
                    <button type="button" class="btn btn-secondary" onclick="prevStep()">السابق</button>
                    <button type="button" class="btn btn-success" onclick="install()">تثبيت النظام</button>
                </div>

                <!-- خطوة اكتمال التثبيت -->
                <div class="step" id="step5">
                    <div class="text-center">
                        <div id="installProgress">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التثبيت...</span>
                            </div>
                            <p class="mt-3">جاري تثبيت النظام، يرجى الانتظار...</p>
                        </div>
                        
                        <div id="installResult" style="display: none;">
                            <div id="successMessage" class="alert alert-success" style="display: none;">
                                <h4>✓ تم تثبيت النظام بنجاح!</h4>
                                <p>يمكنك الآن تسجيل الدخول والبدء في استخدام النظام</p>
                                <a href="/login" class="btn btn-primary">تسجيل الدخول</a>
                            </div>
                            
                            <div id="errorMessage" class="alert alert-danger" style="display: none;">
                                <h4>حدث خطأ أثناء التثبيت</h4>
                                <ul id="errorList"></ul>
                                <button type="button" class="btn btn-secondary" onclick="prevStep()">العودة</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentStep = 1;
        const totalSteps = 5;

        function showStep(step) {
            document.querySelectorAll('.step').forEach(el => el.classList.remove('active'));
            document.getElementById('step' + step).classList.add('active');
        }

        function nextStep() {
            if (currentStep < totalSteps) {
                currentStep++;
                showStep(currentStep);
            }
        }

        function prevStep() {
            if (currentStep > 1) {
                currentStep--;
                showStep(currentStep);
            }
        }

        function install() {
            currentStep = 5;
            showStep(currentStep);
            
            const formData = new FormData(document.getElementById('installForm'));
            
            fetch('install.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('installProgress').style.display = 'none';
                document.getElementById('installResult').style.display = 'block';
                
                if (data.success) {
                    document.getElementById('successMessage').style.display = 'block';
                    if (data.redirect) {
                        setTimeout(() => {
                            window.location.href = data.redirect;
                        }, 3000);
                    }
                } else {
                    document.getElementById('errorMessage').style.display = 'block';
                    const errorList = document.getElementById('errorList');
                    errorList.innerHTML = '';
                    data.errors.forEach(error => {
                        const li = document.createElement('li');
                        li.textContent = error;
                        errorList.appendChild(li);
                    });
                }
            })
            .catch(error => {
                document.getElementById('installProgress').style.display = 'none';
                document.getElementById('installResult').style.display = 'block';
                document.getElementById('errorMessage').style.display = 'block';
                document.getElementById('errorList').innerHTML = '<li>حدث خطأ غير متوقع</li>';
            });
        }
    </script>
</body>
</html>

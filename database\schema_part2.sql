-- الفواتير
CREATE TABLE invoices (
    invoice_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد للفاتورة',
    invoice_number VARCHAR(50) NOT NULL UNIQUE COMMENT 'رقم الفاتورة',
    type ENUM('بيع','شراء') NOT NULL COMMENT 'نوع الفاتورة',
    reference_id INT NOT NULL COMMENT 'رقم المرجع (أمر البيع أو الشراء)',
    customer_id INT NULL COMMENT 'معرف العميل (للفواتير البيع)',
    supplier_id INT NULL COMMENT 'معرف المورد (لفواتير الشراء)',
    user_id INT COMMENT 'معرف المستخدم الذي أنشأ الفاتورة',
    invoice_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الفاتورة',
    due_date DATE COMMENT 'تاريخ الاستحقاق',
    subtotal DECIMAL(12,2) NOT NULL COMMENT 'المجموع الفرعي',
    tax_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT 'قيمة الضريبة',
    discount_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT 'قيمة الخصم',
    total_amount DECIMAL(12,2) NOT NULL COMMENT 'إجمالي المبلغ',
    paid_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT 'المبلغ المدفوع',
    payment_status ENUM('مدفوع','غير مدفوع','جزئي','متأخر') DEFAULT 'غير مدفوع' COMMENT 'حالة الدفع',
    notes TEXT COMMENT 'ملاحظات',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(customer_id),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(supplier_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
) ENGINE=InnoDB COMMENT='جدول الفواتير';

-- تفاصيل الفواتير
CREATE TABLE invoice_items (
    invoice_item_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد لبند الفاتورة',
    invoice_id INT COMMENT 'معرف الفاتورة',
    product_id INT COMMENT 'معرف المنتج',
    quantity DECIMAL(10,2) NOT NULL COMMENT 'الكمية',
    unit_price DECIMAL(10,2) NOT NULL COMMENT 'سعر الوحدة',
    discount_percent DECIMAL(5,2) DEFAULT 0.00 COMMENT 'نسبة الخصم',
    tax_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT 'نسبة الضريبة',
    line_total DECIMAL(12,2) NOT NULL COMMENT 'إجمالي السطر',
    FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(product_id)
) ENGINE=InnoDB COMMENT='جدول تفاصيل الفواتير';

-- المدفوعات
CREATE TABLE payments (
    payment_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد للدفع',
    invoice_id INT COMMENT 'معرف الفاتورة',
    payment_number VARCHAR(50) NOT NULL UNIQUE COMMENT 'رقم الدفعة',
    payment_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الدفع',
    amount DECIMAL(12,2) NOT NULL COMMENT 'المبلغ المدفوع',
    method ENUM('نقدي','تحويل مصرفي','شيك','بطاقة ائتمان','أخرى') DEFAULT 'نقدي' COMMENT 'طريقة الدفع',
    reference_number VARCHAR(100) COMMENT 'رقم المرجع (رقم الشيك، رقم التحويل)',
    bank_name VARCHAR(100) COMMENT 'اسم البنك',
    notes TEXT COMMENT 'ملاحظات',
    user_id INT COMMENT 'معرف المستخدم الذي سجل الدفعة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
) ENGINE=InnoDB COMMENT='جدول المدفوعات';

-- حركة المخزون
CREATE TABLE stock_movements (
    movement_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد للحركة',
    product_id INT COMMENT 'معرف المنتج',
    warehouse_id INT COMMENT 'معرف المستودع',
    movement_type ENUM('إضافة','صرف','تحويل','إرجاع','تسوية','هالك') NOT NULL COMMENT 'نوع الحركة',
    quantity DECIMAL(10,2) NOT NULL COMMENT 'الكمية',
    unit_cost DECIMAL(10,2) COMMENT 'تكلفة الوحدة',
    total_cost DECIMAL(12,2) COMMENT 'التكلفة الإجمالية',
    reference_type ENUM('فاتورة','أمر_بيع','أمر_شراء','تحويل','جرد','أخرى') COMMENT 'نوع المرجع',
    reference_id INT COMMENT 'رقم المرجع',
    from_warehouse_id INT COMMENT 'المستودع المصدر (للتحويلات)',
    to_warehouse_id INT COMMENT 'المستودع الوجهة (للتحويلات)',
    notes TEXT COMMENT 'ملاحظات',
    user_id INT COMMENT 'معرف المستخدم الذي نفذ الحركة',
    movement_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الحركة',
    FOREIGN KEY (product_id) REFERENCES products(product_id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(warehouse_id),
    FOREIGN KEY (from_warehouse_id) REFERENCES warehouses(warehouse_id),
    FOREIGN KEY (to_warehouse_id) REFERENCES warehouses(warehouse_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
) ENGINE=InnoDB COMMENT='جدول حركة المخزون';

-- عروض الأسعار
CREATE TABLE quotations (
    quotation_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد لعرض السعر',
    quotation_number VARCHAR(50) NOT NULL UNIQUE COMMENT 'رقم عرض السعر',
    customer_id INT COMMENT 'معرف العميل',
    user_id INT COMMENT 'معرف المستخدم الذي أنشأ العرض',
    quotation_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ العرض',
    valid_until DATE COMMENT 'صالح حتى تاريخ',
    status ENUM('جديد','مرسل','مقبول','مرفوض','منتهي الصلاحية') DEFAULT 'جديد' COMMENT 'حالة العرض',
    subtotal DECIMAL(12,2) DEFAULT 0.00 COMMENT 'المجموع الفرعي',
    tax_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT 'قيمة الضريبة',
    discount_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT 'قيمة الخصم',
    total_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT 'المجموع الإجمالي',
    notes TEXT COMMENT 'ملاحظات',
    terms_conditions TEXT COMMENT 'الشروط والأحكام',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(customer_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
) ENGINE=InnoDB COMMENT='جدول عروض الأسعار';

-- تفاصيل عروض الأسعار
CREATE TABLE quotation_items (
    quotation_item_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد لبند العرض',
    quotation_id INT COMMENT 'معرف عرض السعر',
    product_id INT COMMENT 'معرف المنتج',
    quantity DECIMAL(10,2) NOT NULL COMMENT 'الكمية',
    unit_price DECIMAL(10,2) NOT NULL COMMENT 'سعر الوحدة',
    discount_percent DECIMAL(5,2) DEFAULT 0.00 COMMENT 'نسبة الخصم',
    tax_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT 'نسبة الضريبة',
    line_total DECIMAL(12,2) NOT NULL COMMENT 'إجمالي السطر',
    notes TEXT COMMENT 'ملاحظات البند',
    FOREIGN KEY (quotation_id) REFERENCES quotations(quotation_id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(product_id)
) ENGINE=InnoDB COMMENT='جدول تفاصيل عروض الأسعار';

-- سجل النشاط
CREATE TABLE audit_log (
    log_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد للسجل',
    user_id INT COMMENT 'معرف المستخدم',
    action VARCHAR(100) NOT NULL COMMENT 'الإجراء المتخذ',
    table_name VARCHAR(50) COMMENT 'اسم الجدول',
    record_id INT COMMENT 'معرف السجل المرتبط',
    old_values JSON COMMENT 'القيم القديمة',
    new_values JSON COMMENT 'القيم الجديدة',
    ip_address VARCHAR(45) COMMENT 'عنوان IP',
    user_agent TEXT COMMENT 'معلومات المتصفح',
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ ووقت الإجراء',
    FOREIGN KEY (user_id) REFERENCES users(user_id)
) ENGINE=InnoDB COMMENT='جدول سجل النشاط';

-- إعدادات النظام
CREATE TABLE system_settings (
    setting_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد للإعداد',
    setting_key VARCHAR(100) NOT NULL UNIQUE COMMENT 'مفتاح الإعداد',
    setting_value TEXT COMMENT 'قيمة الإعداد',
    setting_type ENUM('string','number','boolean','json') DEFAULT 'string' COMMENT 'نوع الإعداد',
    description TEXT COMMENT 'وصف الإعداد',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB COMMENT='جدول إعدادات النظام';

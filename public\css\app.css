/* نظام LYstoreE - ملف الأنماط المخصص */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

/* المتغيرات */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 10px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* الإعدادات العامة */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-color);
    line-height: 1.6;
    color: #333;
}

/* العناوين */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: var(--dark-color);
}

/* الروابط */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-color);
}

/* البطاقات */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    padding: 1rem 1.5rem;
    font-weight: 600;
}

.card-header.bg-transparent {
    background: transparent !important;
    color: var(--dark-color);
    border-bottom: 1px solid #e9ecef;
}

/* الأزرار */
.btn {
    border-radius: var(--border-radius);
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-success {
    background-color: var(--success-color);
}

.btn-danger {
    background-color: var(--danger-color);
}

.btn-warning {
    background-color: var(--warning-color);
    color: #212529;
}

.btn-info {
    background-color: var(--info-color);
}

/* حقول الإدخال */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid #e0e0e0;
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    background: white;
}

.table thead th {
    background-color: #f8f9fa;
    border: none;
    font-weight: 600;
    color: var(--dark-color);
    padding: 1rem;
}

.table tbody td {
    padding: 0.75rem 1rem;
    border-top: 1px solid #e9ecef;
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* الشارات */
.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-weight: 500;
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* القائمة الجانبية */
.sidebar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 100vh;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    margin: 0.25rem 0;
    border-radius: var(--border-radius);
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateX(-5px);
}

.sidebar .nav-link i {
    width: 20px;
    margin-left: 10px;
    text-align: center;
}

/* شريط التنقل العلوي */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* بطاقات الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: var(--box-shadow);
}

.stats-card .stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    display: block;
}

.stats-card .stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* مؤشر التحميل */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    flex-direction: column;
    color: white;
}

.loading.show {
    display: flex;
}

/* الرسوم المتحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        right: -250px;
        width: 250px;
        height: 100vh;
        z-index: 1000;
        transition: var(--transition);
    }
    
    .sidebar.show {
        right: 0;
    }
    
    .main-content {
        margin-right: 0;
        padding: 1rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
}

/* أنماط مخصصة للطباعة */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .alert {
        display: none !important;
    }
    
    .main-content {
        margin: 0;
        padding: 0;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    body {
        background: white;
    }
}

/* أنماط إضافية للنماذج */
.form-floating > label {
    padding: 1rem 0.75rem;
}

.input-group-text {
    background-color: #f8f9fa;
    border-color: #e0e0e0;
}

/* أنماط للقوائم المنسدلة */
.dropdown-menu {
    border: none;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
}

/* أنماط للتبويبات */
.nav-tabs {
    border-bottom: 2px solid #e9ecef;
}

.nav-tabs .nav-link {
    border: none;
    color: var(--dark-color);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.nav-tabs .nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

/* أنماط للمودال */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-footer {
    border: none;
    padding: 1rem 1.5rem;
}

/* أنماط للتقدم */
.progress {
    height: 8px;
    border-radius: 20px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 20px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

/* أنماط للأيقونات */
.icon-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.icon-circle.bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

/* أنماط للنصوص */
.text-primary {
    color: var(--primary-color) !important;
}

.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
}

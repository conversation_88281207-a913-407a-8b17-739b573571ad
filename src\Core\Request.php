<?php

namespace LYstore\Core;

/**
 * فئة إدارة طلبات HTTP
 */
class Request
{
    private $data;
    private $files;
    private $server;

    public function __construct()
    {
        $this->data = array_merge($_GET, $_POST);
        $this->files = $_FILES;
        $this->server = $_SERVER;
    }

    /**
     * الحصول على قيمة من البيانات
     */
    public function get(string $key, $default = null)
    {
        return $this->data[$key] ?? $default;
    }

    /**
     * الحصول على جميع البيانات
     */
    public function all(): array
    {
        return $this->data;
    }

    /**
     * الحصول على بيانات محددة فقط
     */
    public function only(array $keys): array
    {
        return array_intersect_key($this->data, array_flip($keys));
    }

    /**
     * الحصول على جميع البيانات عدا المحددة
     */
    public function except(array $keys): array
    {
        return array_diff_key($this->data, array_flip($keys));
    }

    /**
     * التحقق من وجود مفتاح
     */
    public function has(string $key): bool
    {
        return isset($this->data[$key]);
    }

    /**
     * التحقق من وجود ملف مرفوع
     */
    public function hasFile(string $key): bool
    {
        return isset($this->files[$key]) && $this->files[$key]['error'] === UPLOAD_ERR_OK;
    }

    /**
     * الحصول على ملف مرفوع
     */
    public function file(string $key): ?array
    {
        return $this->hasFile($key) ? $this->files[$key] : null;
    }

    /**
     * الحصول على طريقة الطلب
     */
    public function method(): string
    {
        return strtoupper($this->server['REQUEST_METHOD'] ?? 'GET');
    }

    /**
     * التحقق من طريقة الطلب
     */
    public function isMethod(string $method): bool
    {
        return $this->method() === strtoupper($method);
    }

    /**
     * التحقق من كون الطلب AJAX
     */
    public function isAjax(): bool
    {
        return isset($this->server['HTTP_X_REQUESTED_WITH']) && 
               strtolower($this->server['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    /**
     * الحصول على عنوان IP
     */
    public function ip(): string
    {
        return $this->server['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    /**
     * الحصول على User Agent
     */
    public function userAgent(): string
    {
        return $this->server['HTTP_USER_AGENT'] ?? '';
    }

    /**
     * الحصول على URL الحالي
     */
    public function url(): string
    {
        $protocol = isset($this->server['HTTPS']) && $this->server['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $this->server['HTTP_HOST'] ?? 'localhost';
        $uri = $this->server['REQUEST_URI'] ?? '/';
        
        return $protocol . '://' . $host . $uri;
    }

    /**
     * الحصول على المسار
     */
    public function path(): string
    {
        $uri = $this->server['REQUEST_URI'] ?? '/';
        return parse_url($uri, PHP_URL_PATH);
    }

    /**
     * تنظيف البيانات من الرموز الضارة
     */
    public function sanitize(string $key, $default = null): string
    {
        $value = $this->get($key, $default);
        
        if (is_string($value)) {
            // إزالة الرموز الضارة
            $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
            $value = trim($value);
        }
        
        return $value;
    }

    /**
     * التحقق من صحة CSRF Token
     */
    public function validateCsrfToken(): bool
    {
        $token = $this->get('_token');
        $sessionToken = $_SESSION['_token'] ?? null;
        
        return $token && $sessionToken && hash_equals($sessionToken, $token);
    }

    /**
     * الحصول على البيانات كـ JSON
     */
    public function json(): array
    {
        $input = file_get_contents('php://input');
        return json_decode($input, true) ?? [];
    }
}

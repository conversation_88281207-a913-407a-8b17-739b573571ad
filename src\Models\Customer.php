<?php

namespace LYstore\Models;

use LYstore\Core\Model;

/**
 * نموذج العميل
 */
class Customer extends Model
{
    protected $table = 'customers';
    protected $primaryKey = 'customer_id';
    
    protected $fillable = [
        'name', 'phone', 'email', 'address', 'city', 'type', 
        'credit_limit', 'payment_terms', 'tax_number', 'status'
    ];

    /**
     * البحث في العملاء
     */
    public function searchCustomers(string $term, int $limit = 20): array
    {
        $query = "
            SELECT * FROM {$this->table} 
            WHERE (name LIKE :term OR phone LIKE :term OR email LIKE :term) 
            AND status = 'active'
            ORDER BY name 
            LIMIT {$limit}
        ";
        
        return $this->db->select($query, ['term' => "%{$term}%"]);
    }

    /**
     * الحصول على العملاء حسب النوع
     */
    public function getByType(string $type): array
    {
        return $this->where('type', $type);
    }

    /**
     * الحصول على العملاء النشطين
     */
    public function getActive(): array
    {
        return $this->where('status', 'active');
    }

    /**
     * الحصول على رصيد العميل
     */
    public function getBalance(int $customerId): float
    {
        $query = "
            SELECT 
                COALESCE(SUM(CASE WHEN i.type = 'بيع' THEN i.total_amount - i.paid_amount ELSE 0 END), 0) as balance
            FROM invoices i 
            WHERE i.customer_id = :customer_id AND i.payment_status != 'مدفوع'
        ";
        
        $result = $this->db->selectOne($query, ['customer_id' => $customerId]);
        return (float) $result['balance'];
    }

    /**
     * الحصول على إجمالي مبيعات العميل
     */
    public function getTotalSales(int $customerId, string $fromDate = null, string $toDate = null): float
    {
        $where = 'i.customer_id = :customer_id AND i.type = \'بيع\'';
        $params = ['customer_id' => $customerId];
        
        if ($fromDate) {
            $where .= ' AND i.invoice_date >= :from_date';
            $params['from_date'] = $fromDate;
        }
        
        if ($toDate) {
            $where .= ' AND i.invoice_date <= :to_date';
            $params['to_date'] = $toDate;
        }
        
        $query = "
            SELECT COALESCE(SUM(i.total_amount), 0) as total_sales
            FROM invoices i 
            WHERE {$where}
        ";
        
        $result = $this->db->selectOne($query, $params);
        return (float) $result['total_sales'];
    }

    /**
     * الحصول على آخر المعاملات للعميل
     */
    public function getRecentTransactions(int $customerId, int $limit = 10): array
    {
        $query = "
            SELECT 
                i.invoice_id,
                i.invoice_number,
                i.invoice_date,
                i.total_amount,
                i.paid_amount,
                i.payment_status,
                'فاتورة' as transaction_type
            FROM invoices i 
            WHERE i.customer_id = :customer_id AND i.type = 'بيع'
            
            UNION ALL
            
            SELECT 
                so.order_id as invoice_id,
                so.order_number as invoice_number,
                so.order_date as invoice_date,
                so.total_amount,
                0 as paid_amount,
                so.status as payment_status,
                'أمر بيع' as transaction_type
            FROM sales_orders so 
            WHERE so.customer_id = :customer_id
            
            ORDER BY invoice_date DESC 
            LIMIT {$limit}
        ";
        
        return $this->db->select($query, ['customer_id' => $customerId]);
    }

    /**
     * التحقق من حد الائتمان
     */
    public function checkCreditLimit(int $customerId, float $amount): bool
    {
        $customer = $this->find($customerId);
        if (!$customer) {
            return false;
        }
        
        $currentBalance = $this->getBalance($customerId);
        $creditLimit = (float) $customer['credit_limit'];
        
        return ($currentBalance + $amount) <= $creditLimit;
    }

    /**
     * إحصائيات العملاء
     */
    public function getStats(): array
    {
        $totalQuery = "SELECT COUNT(*) as total FROM {$this->table}";
        $activeQuery = "SELECT COUNT(*) as active FROM {$this->table} WHERE status = 'active'";
        $typeQuery = "
            SELECT type, COUNT(*) as count 
            FROM {$this->table} 
            WHERE status = 'active' 
            GROUP BY type
        ";
        
        $total = $this->db->selectOne($totalQuery)['total'];
        $active = $this->db->selectOne($activeQuery)['active'];
        $byType = $this->db->select($typeQuery);
        
        return [
            'total' => $total,
            'active' => $active,
            'by_type' => $byType
        ];
    }

    /**
     * أفضل العملاء حسب المبيعات
     */
    public function getTopCustomers(int $limit = 10, string $fromDate = null, string $toDate = null): array
    {
        $where = "i.type = 'بيع'";
        $params = [];
        
        if ($fromDate) {
            $where .= ' AND i.invoice_date >= :from_date';
            $params['from_date'] = $fromDate;
        }
        
        if ($toDate) {
            $where .= ' AND i.invoice_date <= :to_date';
            $params['to_date'] = $toDate;
        }
        
        $query = "
            SELECT 
                c.customer_id,
                c.name,
                c.type,
                COALESCE(SUM(i.total_amount), 0) as total_sales,
                COUNT(i.invoice_id) as invoice_count
            FROM {$this->table} c
            LEFT JOIN invoices i ON c.customer_id = i.customer_id AND {$where}
            WHERE c.status = 'active'
            GROUP BY c.customer_id, c.name, c.type
            ORDER BY total_sales DESC
            LIMIT {$limit}
        ";
        
        return $this->db->select($query, $params);
    }

    /**
     * العملاء المتأخرين في السداد
     */
    public function getOverdueCustomers(): array
    {
        $query = "
            SELECT 
                c.customer_id,
                c.name,
                c.phone,
                c.email,
                SUM(i.total_amount - i.paid_amount) as overdue_amount,
                COUNT(i.invoice_id) as overdue_invoices,
                MIN(i.due_date) as oldest_due_date
            FROM {$this->table} c
            INNER JOIN invoices i ON c.customer_id = i.customer_id
            WHERE i.type = 'بيع' 
            AND i.payment_status IN ('غير مدفوع', 'جزئي')
            AND i.due_date < CURDATE()
            AND c.status = 'active'
            GROUP BY c.customer_id, c.name, c.phone, c.email
            ORDER BY overdue_amount DESC
        ";
        
        return $this->db->select($query);
    }
}

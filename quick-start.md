# دليل البدء السريع - نظام LYstoreE

## مرحباً بك في نظام LYstoreE! 🎉

هذا دليل سريع لمساعدتك في تشغيل النظام خلال دقائق معدودة.

## 🚀 البدء السريع

### الطريقة الأولى: استخدام ملفات التشغيل التلقائي

#### على Windows:
```bash
# انقر نقراً مزدوجاً على الملف أو شغله من سطر الأوامر
start.bat
```

#### على Linux/Mac:
```bash
# اجعل الملف قابلاً للتنفيذ ثم شغله
chmod +x start.sh
./start.sh
```

#### باستخدام PHP:
```bash
php start.php
```

### الطريقة الثانية: التشغيل اليدوي

1. **تشغيل خادم التطوير:**
   ```bash
   php -S localhost:8000 -t public
   ```

2. **فتح المتصفح:**
   ```
   http://localhost:8000
   ```

## 📋 المتطلبات الأساسية

- ✅ PHP 8.0 أو أحدث
- ✅ MySQL 8.0 أو أحدث  
- ✅ خادم ويب (Apache/Nginx) أو خادم PHP المدمج
- ✅ امتدادات PHP: PDO, PDO_MySQL, mbstring, JSON, OpenSSL

## 🔧 التثبيت السريع

### الخطوة 1: تحضير البيئة
```bash
# إنشاء المجلدات المطلوبة (تلقائياً عند التشغيل)
mkdir -p storage/{logs,sessions,backups,cache}
mkdir -p public/uploads

# تعيين الصلاحيات (Linux/Mac)
chmod -R 755 storage/
chmod -R 755 public/uploads/
```

### الخطوة 2: إعداد قاعدة البيانات
1. إنشاء قاعدة بيانات جديدة باسم `lystore`
2. تشغيل معالج التثبيت: `http://localhost:8000/install/install.php`
3. اتباع التعليمات لإكمال التثبيت

### الخطوة 3: تسجيل الدخول الأول
- **الرابط:** `http://localhost:8000/login`
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `123456`

> ⚠️ **مهم:** غيّر كلمة المرور فوراً بعد تسجيل الدخول الأول!

## 🎯 الخطوات التالية

### 1. إعداد الشركة
- انتقل إلى الإعدادات وحدّث بيانات شركتك
- أضف شعار الشركة
- حدد العملة والضرائب الافتراضية

### 2. إضافة البيانات الأساسية
- **العملاء:** أضف عملاءك الأساسيين
- **الموردين:** أضف موردينك
- **المنتجات:** أضف منتجاتك مع الأسعار والمخزون
- **المستودعات:** أضف مستودعاتك

### 3. إنشاء المستخدمين
- أضف مستخدمين جدد
- حدد الأدوار والصلاحيات
- اختبر تسجيل الدخول

## 📊 الميزات الرئيسية

### إدارة المبيعات
- ✅ إدارة العملاء
- ✅ عروض الأسعار
- ✅ أوامر البيع
- ✅ الفواتير والمدفوعات

### إدارة المشتريات  
- ✅ إدارة الموردين
- ✅ أوامر الشراء
- ✅ فواتير المشتريات
- ✅ المرتجعات

### إدارة المخازن
- ✅ إدارة المنتجات
- ✅ حركة المخزون
- ✅ الجرد والتنبيهات
- ✅ التحويلات بين المستودعات

### التقارير والتحليلات
- ✅ تقارير المبيعات
- ✅ تقارير المشتريات  
- ✅ تقارير المخزون
- ✅ التقارير المالية

## 🔒 الأمان

### إعدادات الأمان الموصى بها:
1. **تغيير كلمات المرور الافتراضية**
2. **تفعيل HTTPS في الإنتاج**
3. **إجراء نسخ احتياطية دورية**
4. **مراجعة سجلات النشاط**
5. **تحديث النظام بانتظام**

## 🆘 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### خطأ في الاتصال بقاعدة البيانات
```bash
# تحقق من إعدادات .env
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=lystore
DB_USERNAME=root
DB_PASSWORD=your_password
```

#### مشاكل الصلاحيات
```bash
# Linux/Mac
chmod -R 755 storage/
chmod -R 755 public/uploads/

# Windows - تشغيل كمدير
icacls storage /grant Users:F /T
icacls public\uploads /grant Users:F /T
```

#### خطأ 500 - Internal Server Error
1. تحقق من ملفات السجل في `storage/logs/`
2. تأكد من وجود ملف `.env`
3. تحقق من صلاحيات المجلدات

#### صفحة فارغة أو بيضاء
1. تفعيل عرض الأخطاء: `APP_DEBUG=true` في `.env`
2. تحقق من سجل أخطاء PHP
3. تأكد من تثبيت جميع امتدادات PHP المطلوبة

## 📞 الدعم والمساعدة

### الحصول على المساعدة:
- 📧 **البريد الإلكتروني:** <EMAIL>
- 📱 **الهاتف:** +218-91-111-1111
- 🌐 **الموقع:** https://lystore.ly
- 📚 **التوثيق:** [دليل المستخدم الكامل](docs/user-guide.md)

### الموارد المفيدة:
- [دليل المطور](docs/developer-guide.md)
- [API Documentation](docs/api.md)
- [أسئلة شائعة](docs/faq.md)
- [فيديوهات تعليمية](https://youtube.com/lystore)

## 🎉 مبروك!

أنت الآن جاهز لاستخدام نظام LYstoreE! 

### نصائح للبداية:
1. **ابدأ بإدخال بيانات تجريبية** لفهم النظام
2. **استكشف جميع القوائم** للتعرف على الميزات
3. **جرب إنشاء فاتورة بيع** كاملة
4. **راجع التقارير** لفهم البيانات المتاحة
5. **اقرأ دليل المستخدم** للحصول على تفاصيل أكثر

---

**نتمنى لك تجربة ممتعة ومثمرة مع نظام LYstoreE! 🚀**

> 💡 **نصيحة:** احفظ هذا الملف كمرجع سريع للعودة إليه عند الحاجة.

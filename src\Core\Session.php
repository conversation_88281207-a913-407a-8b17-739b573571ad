<?php

namespace LYstore\Core;

/**
 * فئة إدارة الجلسات
 */
class Session
{
    public function __construct()
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }

    /**
     * تعيين قيمة في الجلسة
     */
    public function set(string $key, $value): void
    {
        $_SESSION[$key] = $value;
    }

    /**
     * الحصول على قيمة من الجلسة
     */
    public function get(string $key, $default = null)
    {
        return $_SESSION[$key] ?? $default;
    }

    /**
     * التحقق من وجود مفتاح في الجلسة
     */
    public function has(string $key): bool
    {
        return isset($_SESSION[$key]);
    }

    /**
     * حذف مفتاح من الجلسة
     */
    public function remove(string $key): void
    {
        unset($_SESSION[$key]);
    }

    /**
     * مسح جميع بيانات الجلسة
     */
    public function clear(): void
    {
        $_SESSION = [];
    }

    /**
     * تدمير الجلسة
     */
    public function destroy(): void
    {
        session_destroy();
    }

    /**
     * إعادة إنشاء معرف الجلسة
     */
    public function regenerate(): void
    {
        session_regenerate_id(true);
    }

    /**
     * تعيين رسالة مؤقتة (Flash Message)
     */
    public function setFlash(string $type, string $message): void
    {
        $_SESSION['flash'][$type] = $message;
    }

    /**
     * الحصول على رسالة مؤقتة وحذفها
     */
    public function getFlash(string $type): ?string
    {
        $message = $_SESSION['flash'][$type] ?? null;
        unset($_SESSION['flash'][$type]);
        return $message;
    }

    /**
     * التحقق من وجود رسالة مؤقتة
     */
    public function hasFlash(string $type): bool
    {
        return isset($_SESSION['flash'][$type]);
    }

    /**
     * الحصول على جميع الرسائل المؤقتة
     */
    public function getAllFlash(): array
    {
        $messages = $_SESSION['flash'] ?? [];
        $_SESSION['flash'] = [];
        return $messages;
    }

    /**
     * تسجيل دخول المستخدم
     */
    public function login(array $user): void
    {
        $this->set('user_id', $user['user_id']);
        $this->set('user_name', $user['name']);
        $this->set('username', $user['username']);
        $this->set('user_role', $user['role_name']);
        $this->set('user_role_id', $user['role_id']);
        $this->set('logged_in', true);
        $this->set('login_time', time());
        
        // إعادة إنشاء معرف الجلسة لأمان إضافي
        $this->regenerate();
    }

    /**
     * تسجيل خروج المستخدم
     */
    public function logout(): void
    {
        $this->clear();
        $this->destroy();
    }

    /**
     * التحقق من تسجيل الدخول
     */
    public function isLoggedIn(): bool
    {
        return $this->get('logged_in', false) === true && $this->has('user_id');
    }

    /**
     * الحصول على معرف المستخدم الحالي
     */
    public function getUserId(): ?int
    {
        return $this->get('user_id');
    }

    /**
     * الحصول على اسم المستخدم الحالي
     */
    public function getUserName(): ?string
    {
        return $this->get('user_name');
    }

    /**
     * الحصول على دور المستخدم الحالي
     */
    public function getUserRole(): ?string
    {
        return $this->get('user_role');
    }

    /**
     * التحقق من دور المستخدم
     */
    public function hasRole(string $role): bool
    {
        return $this->getUserRole() === $role;
    }

    /**
     * إنشاء CSRF Token
     */
    public function generateCsrfToken(): string
    {
        $token = bin2hex(random_bytes(32));
        $this->set('_token', $token);
        return $token;
    }

    /**
     * الحصول على CSRF Token
     */
    public function getCsrfToken(): ?string
    {
        return $this->get('_token');
    }

    /**
     * التحقق من صحة CSRF Token
     */
    public function validateCsrfToken(string $token): bool
    {
        $sessionToken = $this->getCsrfToken();
        return $sessionToken && hash_equals($sessionToken, $token);
    }

    /**
     * تعيين آخر نشاط للمستخدم
     */
    public function updateLastActivity(): void
    {
        $this->set('last_activity', time());
    }

    /**
     * التحقق من انتهاء صلاحية الجلسة
     */
    public function isExpired(int $timeout = 7200): bool // 2 ساعة افتراضياً
    {
        $lastActivity = $this->get('last_activity', 0);
        return (time() - $lastActivity) > $timeout;
    }

    /**
     * تخزين البيانات المؤقتة
     */
    public function setTemp(string $key, $value, int $ttl = 300): void // 5 دقائق افتراضياً
    {
        $_SESSION['temp'][$key] = [
            'value' => $value,
            'expires' => time() + $ttl
        ];
    }

    /**
     * الحصول على البيانات المؤقتة
     */
    public function getTemp(string $key, $default = null)
    {
        if (!isset($_SESSION['temp'][$key])) {
            return $default;
        }

        $data = $_SESSION['temp'][$key];
        
        if (time() > $data['expires']) {
            unset($_SESSION['temp'][$key]);
            return $default;
        }

        return $data['value'];
    }

    /**
     * تنظيف البيانات المؤقتة المنتهية الصلاحية
     */
    public function cleanExpiredTemp(): void
    {
        if (!isset($_SESSION['temp'])) {
            return;
        }

        $currentTime = time();
        foreach ($_SESSION['temp'] as $key => $data) {
            if ($currentTime > $data['expires']) {
                unset($_SESSION['temp'][$key]);
            }
        }
    }
}

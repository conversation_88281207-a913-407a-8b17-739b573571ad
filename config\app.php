<?php
/**
 * إعدادات التطبيق الرئيسية لنظام LYstoreE
 */

return [
    // معلومات التطبيق
    'name' => 'LYstoreE',
    'version' => '1.0.0',
    'description' => 'نظام إدارة المبيعات والمشتريات والمخازن',
    
    // إعدادات البيئة
    'env' => $_ENV['APP_ENV'] ?? 'production',
    'debug' => $_ENV['APP_DEBUG'] ?? false,
    'url' => $_ENV['APP_URL'] ?? 'http://localhost',
    
    // إعدادات الأمان
    'key' => $_ENV['APP_KEY'] ?? 'base64:' . base64_encode(random_bytes(32)),
    'cipher' => 'AES-256-CBC',
    
    // إعدادات المنطقة الزمنية واللغة
    'timezone' => 'Africa/Tripoli',
    'locale' => 'ar',
    'fallback_locale' => 'en',
    
    // إعدادات الجلسة
    'session' => [
        'driver' => 'file',
        'lifetime' => 120, // دقيقة
        'expire_on_close' => false,
        'encrypt' => false,
        'files' => storage_path('sessions'),
        'connection' => null,
        'table' => 'sessions',
        'store' => null,
        'lottery' => [2, 100],
        'cookie' => 'lystore_session',
        'path' => '/',
        'domain' => null,
        'secure' => false,
        'http_only' => true,
        'same_site' => 'lax',
    ],
    
    // إعدادات الملفات المرفوعة
    'upload' => [
        'max_size' => 10 * 1024 * 1024, // 10 ميجابايت
        'allowed_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx'],
        'path' => 'uploads/',
    ],
    
    // إعدادات التقارير
    'reports' => [
        'default_format' => 'pdf',
        'supported_formats' => ['pdf', 'excel', 'csv'],
        'cache_duration' => 3600, // ثانية
    ],
    
    // إعدادات النسخ الاحتياطي
    'backup' => [
        'enabled' => true,
        'frequency' => 'daily',
        'retention_days' => 30,
        'path' => storage_path('backups'),
    ],
    
    // إعدادات التسجيل
    'logging' => [
        'default' => 'daily',
        'channels' => [
            'daily' => [
                'driver' => 'daily',
                'path' => storage_path('logs/lystore.log'),
                'level' => 'debug',
                'days' => 14,
            ],
            'single' => [
                'driver' => 'single',
                'path' => storage_path('logs/lystore.log'),
                'level' => 'debug',
            ],
        ],
    ],
];

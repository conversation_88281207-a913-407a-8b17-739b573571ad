<?php

namespace LYstore\Middlewares;

use LYstore\Core\Request;
use LYstore\Core\Response;
use LYstore\Core\Session;

/**
 * وسطاء التحقق من المصادقة
 */
class Auth
{
    private $session;

    public function __construct()
    {
        $this->session = new Session();
    }

    /**
     * معالجة الطلب
     */
    public function handle(Request $request, Response $response): void
    {
        // التحقق من تسجيل الدخول
        if (!$this->session->isLoggedIn()) {
            $this->handleUnauthenticated($request, $response);
            return;
        }

        // التحقق من انتهاء صلاحية الجلسة
        if ($this->session->isExpired()) {
            $this->session->logout();
            $this->handleSessionExpired($request, $response);
            return;
        }

        // تحديث آخر نشاط
        $this->session->updateLastActivity();
    }

    /**
     * معالجة المستخدم غير المصادق عليه
     */
    private function handleUnauthenticated(Request $request, Response $response): void
    {
        if ($request->isAjax()) {
            $response->json([
                'error' => 'يجب تسجيل الدخول للوصول لهذه الصفحة',
                'redirect' => '/login'
            ], 401);
        } else {
            $response->redirect('/login');
        }
    }

    /**
     * معالجة انتهاء صلاحية الجلسة
     */
    private function handleSessionExpired(Request $request, Response $response): void
    {
        if ($request->isAjax()) {
            $response->json([
                'error' => 'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى',
                'redirect' => '/login'
            ], 401);
        } else {
            $this->session->setFlash('warning', 'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى');
            $response->redirect('/login');
        }
    }
}

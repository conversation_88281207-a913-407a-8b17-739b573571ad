<?php

namespace LYstore\Middlewares;

use LYstore\Core\Request;
use LYstore\Core\Response;
use LYstore\Core\Session;

/**
 * وسطاء التحقق من صلاحيات المدير
 */
class AdminOnly
{
    private $session;

    public function __construct()
    {
        $this->session = new Session();
    }

    /**
     * معالجة الطلب
     */
    public function handle(Request $request, Response $response): void
    {
        // التحقق من تسجيل الدخول أولاً
        if (!$this->session->isLoggedIn()) {
            $this->handleUnauthorized($request, $response, 'يجب تسجيل الدخول أولاً');
            return;
        }

        // التحقق من كون المستخدم مدير نظام
        if (!$this->session->hasRole('مدير النظام')) {
            $this->handleUnauthorized($request, $response, 'ليس لديك صلاحية للوصول لهذه الصفحة');
            return;
        }
    }

    /**
     * معالجة عدم وجود صلاحية
     */
    private function handleUnauthorized(Request $request, Response $response, string $message): void
    {
        if ($request->isAjax()) {
            $response->json([
                'error' => $message
            ], 403);
        } else {
            $this->session->setFlash('error', $message);
            $response->redirect('/dashboard');
        }
    }
}

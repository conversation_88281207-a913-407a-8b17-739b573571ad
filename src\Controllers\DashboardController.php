<?php

namespace LYstore\Controllers;

use LYstore\Core\Controller;
use LYstore\Models\Customer;
use LYstore\Models\Product;
use LYstore\Models\User;

/**
 * متحكم لوحة التحكم
 */
class DashboardController extends Controller
{
    private $customerModel;
    private $productModel;
    private $userModel;

    public function __construct()
    {
        parent::__construct();
        $this->customerModel = new Customer();
        $this->productModel = new Product();
        $this->userModel = new User();
    }

    /**
     * عرض لوحة التحكم الرئيسية
     */
    public function index(): void
    {
        $this->requireAuth();

        try {
            // الحصول على الإحصائيات
            $stats = $this->getDashboardStats();
            
            // الحصول على البيانات الحديثة
            $recentData = $this->getRecentData();
            
            // الحصول على التنبيهات
            $alerts = $this->getAlerts();

            $this->view('dashboard.index', [
                'title' => 'لوحة التحكم',
                'stats' => $stats,
                'recent_data' => $recentData,
                'alerts' => $alerts,
                'user' => [
                    'name' => $this->session->getUserName(),
                    'role' => $this->session->getUserRole()
                ]
            ]);

        } catch (\Exception $e) {
            $this->handleError($e, 'حدث خطأ في تحميل لوحة التحكم');
        }
    }

    /**
     * الحصول على إحصائيات لوحة التحكم
     */
    private function getDashboardStats(): array
    {
        $stats = [];

        try {
            // إحصائيات العملاء
            $customerStats = $this->customerModel->getStats();
            $stats['customers'] = [
                'total' => $customerStats['total'],
                'active' => $customerStats['active'],
                'new_this_month' => $this->getNewCustomersThisMonth()
            ];

            // إحصائيات المنتجات
            $productStats = $this->getProductStats();
            $stats['products'] = [
                'total' => $productStats['total'],
                'active' => $productStats['active'],
                'low_stock' => $productStats['low_stock']
            ];

            // إحصائيات المبيعات
            $salesStats = $this->getSalesStats();
            $stats['sales'] = [
                'today' => $salesStats['today'],
                'this_month' => $salesStats['this_month'],
                'this_year' => $salesStats['this_year']
            ];

            // إحصائيات المشتريات
            $purchaseStats = $this->getPurchaseStats();
            $stats['purchases'] = [
                'today' => $purchaseStats['today'],
                'this_month' => $purchaseStats['this_month'],
                'pending_orders' => $purchaseStats['pending_orders']
            ];

            // إحصائيات المستخدمين (للمدير فقط)
            if ($this->session->hasRole('مدير النظام')) {
                $userStats = $this->userModel->getStats();
                $stats['users'] = [
                    'total' => $userStats['total'],
                    'active' => $userStats['active'],
                    'online' => $this->getOnlineUsers()
                ];
            }

        } catch (\Exception $e) {
            error_log('خطأ في الحصول على إحصائيات لوحة التحكم: ' . $e->getMessage());
        }

        return $stats;
    }

    /**
     * الحصول على البيانات الحديثة
     */
    private function getRecentData(): array
    {
        $data = [];

        try {
            // أحدث العملاء
            $data['recent_customers'] = $this->db->select(
                "SELECT customer_id, name, type, created_at 
                 FROM customers 
                 WHERE status = 'active' 
                 ORDER BY created_at DESC 
                 LIMIT 5"
            );

            // أحدث أوامر البيع
            $data['recent_sales_orders'] = $this->db->select(
                "SELECT so.order_id, so.order_number, so.order_date, so.total_amount, so.status,
                        c.name as customer_name
                 FROM sales_orders so
                 LEFT JOIN customers c ON so.customer_id = c.customer_id
                 ORDER BY so.order_date DESC 
                 LIMIT 5"
            );

            // أحدث أوامر الشراء
            $data['recent_purchase_orders'] = $this->db->select(
                "SELECT po.po_id, po.po_number, po.order_date, po.total_amount, po.status,
                        s.name as supplier_name
                 FROM purchase_orders po
                 LEFT JOIN suppliers s ON po.supplier_id = s.supplier_id
                 ORDER BY po.order_date DESC 
                 LIMIT 5"
            );

            // المنتجات منخفضة المخزون
            $data['low_stock_products'] = $this->db->select(
                "SELECT product_id, name, current_stock, min_stock_level, unit_price
                 FROM products 
                 WHERE current_stock <= min_stock_level 
                 AND status = 'active'
                 ORDER BY (current_stock / min_stock_level) ASC
                 LIMIT 10"
            );

        } catch (\Exception $e) {
            error_log('خطأ في الحصول على البيانات الحديثة: ' . $e->getMessage());
        }

        return $data;
    }

    /**
     * الحصول على التنبيهات
     */
    private function getAlerts(): array
    {
        $alerts = [];

        try {
            // تنبيهات المخزون المنخفض
            $lowStockCount = $this->db->selectOne(
                "SELECT COUNT(*) as count 
                 FROM products 
                 WHERE current_stock <= min_stock_level 
                 AND status = 'active'"
            )['count'];

            if ($lowStockCount > 0) {
                $alerts[] = [
                    'type' => 'warning',
                    'title' => 'تنبيه مخزون منخفض',
                    'message' => "يوجد {$lowStockCount} منتج بمخزون منخفض",
                    'link' => '/products?filter=low_stock'
                ];
            }

            // تنبيهات الفواتير المتأخرة
            $overdueInvoices = $this->db->selectOne(
                "SELECT COUNT(*) as count 
                 FROM invoices 
                 WHERE type = 'بيع' 
                 AND payment_status IN ('غير مدفوع', 'جزئي')
                 AND due_date < CURDATE()"
            )['count'];

            if ($overdueInvoices > 0) {
                $alerts[] = [
                    'type' => 'danger',
                    'title' => 'فواتير متأخرة السداد',
                    'message' => "يوجد {$overdueInvoices} فاتورة متأخرة السداد",
                    'link' => '/invoices?filter=overdue'
                ];
            }

            // تنبيهات أوامر الشراء المعلقة
            $pendingPOs = $this->db->selectOne(
                "SELECT COUNT(*) as count 
                 FROM purchase_orders 
                 WHERE status IN ('جديد', 'مرسل')"
            )['count'];

            if ($pendingPOs > 0) {
                $alerts[] = [
                    'type' => 'info',
                    'title' => 'أوامر شراء معلقة',
                    'message' => "يوجد {$pendingPOs} أمر شراء في انتظار المعالجة",
                    'link' => '/purchase-orders?filter=pending'
                ];
            }

        } catch (\Exception $e) {
            error_log('خطأ في الحصول على التنبيهات: ' . $e->getMessage());
        }

        return $alerts;
    }

    /**
     * الحصول على عدد العملاء الجدد هذا الشهر
     */
    private function getNewCustomersThisMonth(): int
    {
        try {
            $result = $this->db->selectOne(
                "SELECT COUNT(*) as count 
                 FROM customers 
                 WHERE YEAR(created_at) = YEAR(CURDATE()) 
                 AND MONTH(created_at) = MONTH(CURDATE())"
            );
            return (int) $result['count'];
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * الحصول على إحصائيات المنتجات
     */
    private function getProductStats(): array
    {
        try {
            $total = $this->db->selectOne("SELECT COUNT(*) as count FROM products")['count'];
            $active = $this->db->selectOne("SELECT COUNT(*) as count FROM products WHERE status = 'active'")['count'];
            $lowStock = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM products WHERE current_stock <= min_stock_level AND status = 'active'"
            )['count'];

            return [
                'total' => (int) $total,
                'active' => (int) $active,
                'low_stock' => (int) $lowStock
            ];
        } catch (\Exception $e) {
            return ['total' => 0, 'active' => 0, 'low_stock' => 0];
        }
    }

    /**
     * الحصول على إحصائيات المبيعات
     */
    private function getSalesStats(): array
    {
        try {
            $today = $this->db->selectOne(
                "SELECT COALESCE(SUM(total_amount), 0) as total 
                 FROM invoices 
                 WHERE type = 'بيع' AND DATE(invoice_date) = CURDATE()"
            )['total'];

            $thisMonth = $this->db->selectOne(
                "SELECT COALESCE(SUM(total_amount), 0) as total 
                 FROM invoices 
                 WHERE type = 'بيع' 
                 AND YEAR(invoice_date) = YEAR(CURDATE()) 
                 AND MONTH(invoice_date) = MONTH(CURDATE())"
            )['total'];

            $thisYear = $this->db->selectOne(
                "SELECT COALESCE(SUM(total_amount), 0) as total 
                 FROM invoices 
                 WHERE type = 'بيع' AND YEAR(invoice_date) = YEAR(CURDATE())"
            )['total'];

            return [
                'today' => (float) $today,
                'this_month' => (float) $thisMonth,
                'this_year' => (float) $thisYear
            ];
        } catch (\Exception $e) {
            return ['today' => 0, 'this_month' => 0, 'this_year' => 0];
        }
    }

    /**
     * الحصول على إحصائيات المشتريات
     */
    private function getPurchaseStats(): array
    {
        try {
            $today = $this->db->selectOne(
                "SELECT COALESCE(SUM(total_amount), 0) as total 
                 FROM invoices 
                 WHERE type = 'شراء' AND DATE(invoice_date) = CURDATE()"
            )['total'];

            $thisMonth = $this->db->selectOne(
                "SELECT COALESCE(SUM(total_amount), 0) as total 
                 FROM invoices 
                 WHERE type = 'شراء' 
                 AND YEAR(invoice_date) = YEAR(CURDATE()) 
                 AND MONTH(invoice_date) = MONTH(CURDATE())"
            )['total'];

            $pendingOrders = $this->db->selectOne(
                "SELECT COUNT(*) as count 
                 FROM purchase_orders 
                 WHERE status IN ('جديد', 'مرسل')"
            )['count'];

            return [
                'today' => (float) $today,
                'this_month' => (float) $thisMonth,
                'pending_orders' => (int) $pendingOrders
            ];
        } catch (\Exception $e) {
            return ['today' => 0, 'this_month' => 0, 'pending_orders' => 0];
        }
    }

    /**
     * الحصول على عدد المستخدمين المتصلين
     */
    private function getOnlineUsers(): int
    {
        // يمكن تطوير هذه الوظيفة لاحقاً لتتبع المستخدمين المتصلين
        return 1;
    }
}
